from rest_framework import serializers
from django.core.validators import RegexValidator
from .models import (
    ProviderProfile, ProviderDocument, ProviderBankDetails, 
    ProviderPayoutRequest, ProviderAvailability
)


class ProviderProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for ProviderProfile model.
    """
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    user_mobile = serializers.Char<PERSON>ield(source='user.mobile', read_only=True)
    user_email = serializers.Char<PERSON>ield(source='user.email', read_only=True)
    verified_by_name = serializers.Char<PERSON>ield(source='verified_by.get_full_name', read_only=True)
    specializations_names = serializers.StringRelatedField(source='specializations', many=True, read_only=True)
    services_offered_names = serializers.StringRelatedField(source='services_offered', many=True, read_only=True)

    class Meta:
        model = ProviderProfile
        fields = [
            'id', 'user', 'user_name', 'user_mobile', 'user_email',
            'business_name', 'business_type', 'years_of_experience',
            'specializations', 'specializations_names', 'services_offered', 'services_offered_names',
            'service_areas', 'working_hours', 'verification_status', 'verified_at',
            'verified_by', 'verified_by_name', 'average_rating', 'total_reviews',
            'total_orders_completed', 'commission_rate', 'is_available',
            'accepts_new_orders', 'profile_description', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'user', 'verification_status', 'verified_at', 'verified_by',
            'average_rating', 'total_reviews', 'total_orders_completed',
            'created_at', 'updated_at'
        ]


class ProviderProfileUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating provider profile.
    """
    class Meta:
        model = ProviderProfile
        fields = [
            'business_name', 'business_type', 'years_of_experience',
            'specializations', 'services_offered', 'service_areas',
            'working_hours', 'is_available', 'accepts_new_orders',
            'profile_description'
        ]


class ProviderDocumentSerializer(serializers.ModelSerializer):
    """
    Serializer for ProviderDocument model.
    """
    provider_name = serializers.CharField(source='provider.user.get_full_name', read_only=True)
    verified_by_name = serializers.CharField(source='verified_by.get_full_name', read_only=True)
    is_expired = serializers.ReadOnlyField()

    class Meta:
        model = ProviderDocument
        fields = [
            'id', 'provider', 'provider_name', 'document_type', 'document_file',
            'document_number', 'verification_status', 'verified_by', 'verified_by_name',
            'verified_at', 'rejection_reason', 'issued_date', 'expiry_date',
            'is_expired', 'uploaded_at', 'updated_at'
        ]
        read_only_fields = [
            'verification_status', 'verified_by', 'verified_at',
            'uploaded_at', 'updated_at'
        ]


class DocumentUploadSerializer(serializers.Serializer):
    """
    Serializer for document upload.
    """
    document_type = serializers.ChoiceField(choices=ProviderDocument.DOCUMENT_TYPE_CHOICES)
    document_file = serializers.FileField()
    document_number = serializers.CharField(max_length=100, required=False)
    issued_date = serializers.DateField(required=False)
    expiry_date = serializers.DateField(required=False)


class ProviderBankDetailsSerializer(serializers.ModelSerializer):
    """
    Serializer for ProviderBankDetails model.
    """
    provider_name = serializers.CharField(source='provider.user.get_full_name', read_only=True)
    verified_by_name = serializers.CharField(source='verified_by.get_full_name', read_only=True)

    class Meta:
        model = ProviderBankDetails
        fields = [
            'id', 'provider', 'provider_name', 'account_holder_name',
            'account_number', 'ifsc_code', 'bank_name', 'branch_name',
            'upi_id', 'is_verified', 'verified_by', 'verified_by_name',
            'verified_at', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'provider', 'is_verified', 'verified_by', 'verified_at',
            'created_at', 'updated_at'
        ]


class ProviderPayoutRequestSerializer(serializers.ModelSerializer):
    """
    Serializer for ProviderPayoutRequest model.
    """
    provider_name = serializers.CharField(source='provider.user.get_full_name', read_only=True)
    provider_mobile = serializers.CharField(source='provider.user.mobile', read_only=True)
    processed_by_name = serializers.CharField(source='processed_by.get_full_name', read_only=True)

    class Meta:
        model = ProviderPayoutRequest
        fields = [
            'id', 'provider', 'provider_name', 'provider_mobile', 'amount',
            'status', 'processed_by', 'processed_by_name', 'processed_at',
            'transaction_id', 'provider_notes', 'admin_notes', 'rejection_reason',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'provider', 'processed_by', 'processed_at', 'transaction_id',
            'admin_notes', 'rejection_reason', 'created_at', 'updated_at'
        ]


class PayoutRequestSerializer(serializers.Serializer):
    """
    Serializer for creating payout request.
    """
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    provider_notes = serializers.CharField(max_length=500, required=False)

    def validate_amount(self, value):
        if value <= 0:
            raise serializers.ValidationError("Amount must be greater than zero")
        return value


class ProviderAvailabilitySerializer(serializers.ModelSerializer):
    """
    Serializer for ProviderAvailability model.
    """
    provider_name = serializers.CharField(source='provider.user.get_full_name', read_only=True)

    class Meta:
        model = ProviderAvailability
        fields = [
            'id', 'provider', 'provider_name', 'date', 'start_time',
            'end_time', 'is_available', 'is_booked', 'booked_order_id',
            'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['provider', 'is_booked', 'booked_order_id', 'created_at', 'updated_at']


class UpdateAvailabilitySerializer(serializers.Serializer):
    """
    Serializer for updating provider availability.
    """
    date = serializers.DateField()
    start_time = serializers.TimeField()
    end_time = serializers.TimeField()
    is_available = serializers.BooleanField(default=True)
    notes = serializers.CharField(max_length=500, required=False)

    def validate(self, data):
        if data['start_time'] >= data['end_time']:
            raise serializers.ValidationError("Start time must be before end time")
        return data


class ProviderVerificationSerializer(serializers.Serializer):
    """
    Serializer for provider verification.
    """
    verification_status = serializers.ChoiceField(choices=ProviderProfile.VERIFICATION_STATUS_CHOICES)
    notes = serializers.CharField(max_length=500, required=False)


class DocumentVerificationSerializer(serializers.Serializer):
    """
    Serializer for document verification.
    """
    verification_status = serializers.ChoiceField(choices=ProviderDocument.VERIFICATION_STATUS_CHOICES)
    rejection_reason = serializers.CharField(max_length=500, required=False)


class ProcessPayoutSerializer(serializers.Serializer):
    """
    Serializer for processing payout.
    """
    action = serializers.ChoiceField(choices=[('approve', 'Approve'), ('reject', 'Reject')])
    admin_notes = serializers.CharField(max_length=500, required=False)
    rejection_reason = serializers.CharField(max_length=500, required=False)

    def validate(self, data):
        if data['action'] == 'reject' and not data.get('rejection_reason'):
            raise serializers.ValidationError("Rejection reason is required when rejecting payout")
        return data


class ProviderListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for provider listings.
    """
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    user_mobile = serializers.CharField(source='user.mobile', read_only=True)

    class Meta:
        model = ProviderProfile
        fields = [
            'id', 'user_name', 'user_mobile', 'business_name',
            'verification_status', 'average_rating', 'total_orders_completed',
            'is_available', 'accepts_new_orders', 'created_at'
        ]


class ProviderDashboardSerializer(serializers.Serializer):
    """
    Serializer for provider dashboard data.
    """
    total_orders = serializers.IntegerField()
    pending_orders = serializers.IntegerField()
    completed_orders = serializers.IntegerField()
    total_earnings = serializers.DecimalField(max_digits=10, decimal_places=2)
    pending_payouts = serializers.DecimalField(max_digits=10, decimal_places=2)
    average_rating = serializers.DecimalField(max_digits=3, decimal_places=2)
    verification_status = serializers.CharField()
    documents_pending = serializers.IntegerField()
