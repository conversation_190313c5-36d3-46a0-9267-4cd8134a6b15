from django.contrib.auth.models import BaseUserManager
from django.core.exceptions import ValidationError


class UserManager(BaseUserManager):
    """
    Custom user manager for handling email and mobile-based authentication
    """
    
    def create_user(self, email=None, mobile_number=None, name=None, user_type='CUSTOMER', password=None, **extra_fields):
        """
        Create and return a regular user
        """
        if not name:
            raise ValueError('Users must have a name')
        
        # Validate based on user type
        if user_type == 'STAFF':
            if not email:
                raise ValueError('Staff users must have an email address')
            email = self.normalize_email(email)
        elif user_type in ['CUSTOMER', 'PROVIDER']:
            if not mobile_number:
                raise ValueError('Customer and Provider users must have a mobile number')
        else:
            raise ValueError('Invalid user type')
        
        # Set default values
        extra_fields.setdefault('is_active', True)
        extra_fields.setdefault('is_staff', False)
        extra_fields.setdefault('is_superuser', False)
        
        # Create user instance
        user = self.model(
            email=email,
            mobile_number=mobile_number,
            name=name,
            user_type=user_type,
            **extra_fields
        )
        
        if password:
            user.set_password(password)
        user.save(using=self._db)
        return user
    
    def create_staff_user(self, email, name, password=None, **extra_fields):
        """
        Create and return a staff user
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('user_type', 'STAFF')
        
        return self.create_user(
            email=email,
            name=name,
            password=password,
            user_type='STAFF',
            **extra_fields
        )
    
    def create_superuser(self, email, name, password=None, **extra_fields):
        """
        Create and return a superuser
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_verified', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        # Remove user_type from extra_fields if it exists to avoid conflict
        extra_fields.pop('user_type', None)

        return self.create_user(
            email=email,
            name=name,
            password=password,
            user_type='STAFF',
            **extra_fields
        )
    
    def create_customer(self, mobile_number, name, **extra_fields):
        """
        Create and return a customer user
        """
        extra_fields.setdefault('user_type', 'CUSTOMER')
        return self.create_user(
            mobile_number=mobile_number,
            name=name,
            user_type='CUSTOMER',
            **extra_fields
        )
    
    def create_provider(self, mobile_number, name, **extra_fields):
        """
        Create and return a provider user
        """
        extra_fields.setdefault('user_type', 'PROVIDER')
        return self.create_user(
            mobile_number=mobile_number,
            name=name,
            user_type='PROVIDER',
            **extra_fields
        )
    
    def get_by_natural_key(self, username):
        """
        Allow authentication by email or mobile number
        """
        # Try to find by email first
        try:
            return self.get(email=username)
        except self.model.DoesNotExist:
            pass
        
        # Try to find by mobile number
        try:
            return self.get(mobile_number=username)
        except self.model.DoesNotExist:
            raise self.model.DoesNotExist(
                f'{self.model._meta.object_name} matching query does not exist.'
            )
