from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsA<PERSON>ent<PERSON>ted, IsAdminUser
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.conf import settings
from django.utils import timezone
from decimal import Decimal
import razorpay
import hashlib
import hmac
import json
from .models import PaymentTransaction, RazorpayPayment, CODPayment, PaymentRefund, PaymentWebhook
from .serializers import (
    PaymentTransactionSerializer, InitiatePaymentSerializer,
    PaymentVerificationSerializer, RefundSerializer, CODConfirmationSerializer,
    InitiateRefundSerializer
)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def initiate_payment(request):
    """
    Initiate payment for an order.
    """
    serializer = InitiatePaymentSerializer(data=request.data)

    if serializer.is_valid():
        order_id = serializer.validated_data['order_id']
        amount = serializer.validated_data['amount']
        payment_method = serializer.validated_data['payment_method']
        currency = serializer.validated_data['currency']

        try:
            # Get order details
            from orders.models import Order
            order = Order.objects.get(id=order_id, customer=request.user)

            # Create payment transaction
            transaction = PaymentTransaction.objects.create(
                order_id=str(order.id),
                order_number=order.order_number,
                user=request.user,
                payment_method=payment_method,
                amount=amount,
                currency=currency,
                status='initiated'
            )

            if payment_method == 'razorpay':
                # Initialize Razorpay client
                client = razorpay.Client(
                    auth=(
                        getattr(settings, 'RAZORPAY_KEY_ID', ''),
                        getattr(settings, 'RAZORPAY_KEY_SECRET', '')
                    )
                )

                # Create Razorpay order
                razorpay_order = client.order.create({
                    'amount': int(amount * 100),  # Amount in paise
                    'currency': currency,
                    'receipt': transaction.transaction_id,
                    'payment_capture': 1
                })

                # Create Razorpay payment record
                RazorpayPayment.objects.create(
                    transaction=transaction,
                    razorpay_order_id=razorpay_order['id']
                )

                transaction.gateway_transaction_id = razorpay_order['id']
                transaction.status = 'pending'
                transaction.save()

                return Response({
                    'success': True,
                    'transaction_id': transaction.transaction_id,
                    'razorpay_order_id': razorpay_order['id'],
                    'razorpay_key_id': getattr(settings, 'RAZORPAY_KEY_ID', ''),
                    'amount': amount,
                    'currency': currency,
                    'order_number': order.order_number
                })

            elif payment_method == 'cod':
                # Create COD payment record
                CODPayment.objects.create(transaction=transaction)

                transaction.status = 'pending'
                transaction.save()

                return Response({
                    'success': True,
                    'transaction_id': transaction.transaction_id,
                    'message': 'COD payment initiated successfully'
                })

        except Order.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Order not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Payment initiation failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def razorpay_callback(request):
    """
    Handle Razorpay payment callback.
    """
    serializer = PaymentVerificationSerializer(data=request.data)

    if serializer.is_valid():
        transaction_id = serializer.validated_data['transaction_id']
        razorpay_payment_id = serializer.validated_data.get('razorpay_payment_id')
        razorpay_order_id = serializer.validated_data.get('razorpay_order_id')
        razorpay_signature = serializer.validated_data.get('razorpay_signature')

        try:
            transaction = PaymentTransaction.objects.get(
                transaction_id=transaction_id,
                user=request.user
            )

            # Verify signature
            client = razorpay.Client(
                auth=(
                    getattr(settings, 'RAZORPAY_KEY_ID', ''),
                    getattr(settings, 'RAZORPAY_KEY_SECRET', '')
                )
            )

            # Verify payment signature
            params_dict = {
                'razorpay_order_id': razorpay_order_id,
                'razorpay_payment_id': razorpay_payment_id,
                'razorpay_signature': razorpay_signature
            }

            try:
                client.utility.verify_payment_signature(params_dict)

                # Update transaction
                transaction.gateway_payment_id = razorpay_payment_id
                transaction.gateway_signature = razorpay_signature
                transaction.mark_success()

                # Update Razorpay payment record
                razorpay_payment = transaction.razorpay_details
                razorpay_payment.razorpay_payment_id = razorpay_payment_id
                razorpay_payment.razorpay_signature = razorpay_signature
                razorpay_payment.save()

                # Update order status
                from orders.models import Order
                order = Order.objects.get(id=transaction.order_id)
                order.payment_status = 'paid'
                order.payment_id = razorpay_payment_id
                order.payment_signature = razorpay_signature
                if order.status == 'pending':
                    order.status = 'confirmed'
                order.save()

                return Response({
                    'success': True,
                    'message': 'Payment verified successfully',
                    'transaction': PaymentTransactionSerializer(transaction).data
                })

            except razorpay.errors.SignatureVerificationError:
                transaction.mark_failed('Invalid payment signature', 'SIGNATURE_VERIFICATION_FAILED')
                return Response({
                    'success': False,
                    'message': 'Payment verification failed'
                }, status=status.HTTP_400_BAD_REQUEST)

        except PaymentTransaction.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Transaction not found'
            }, status=status.HTTP_404_NOT_FOUND)

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def cod_confirm(request):
    """
    Confirm Cash on Delivery payment (for service providers).
    """
    serializer = CODConfirmationSerializer(data=request.data)

    if serializer.is_valid():
        transaction_id = serializer.validated_data['transaction_id']
        collected_amount = serializer.validated_data['collected_amount']
        collection_notes = serializer.validated_data.get('collection_notes', '')

        try:
            transaction = PaymentTransaction.objects.get(
                transaction_id=transaction_id,
                payment_method='cod'
            )

            # Only allow service provider or staff to confirm COD
            if request.user.user_type not in ['provider', 'staff']:
                return Response({
                    'success': False,
                    'message': 'Only service providers or staff can confirm COD payments'
                }, status=status.HTTP_403_FORBIDDEN)

            # Update COD payment
            cod_payment = transaction.cod_details
            cod_payment.mark_collected(collected_amount, request.user, collection_notes)

            # Update order status
            from orders.models import Order
            order = Order.objects.get(id=transaction.order_id)
            order.payment_status = 'paid'
            if order.status == 'pending':
                order.status = 'confirmed'
            order.save()

            return Response({
                'success': True,
                'message': 'COD payment confirmed successfully',
                'transaction': PaymentTransactionSerializer(transaction).data
            })

        except PaymentTransaction.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Transaction not found'
            }, status=status.HTTP_404_NOT_FOUND)

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def payment_status(request, transaction_id):
    """
    Get payment status for a transaction.
    """
    try:
        if request.user.user_type == 'staff':
            transaction = PaymentTransaction.objects.get(transaction_id=transaction_id)
        else:
            transaction = PaymentTransaction.objects.get(
                transaction_id=transaction_id,
                user=request.user
            )

        return Response({
            'success': True,
            'transaction': PaymentTransactionSerializer(transaction).data
        })

    except PaymentTransaction.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Transaction not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def verify_payment(request):
    """
    Verify payment signature and update status.
    """
    # This is handled by razorpay_callback, but keeping for compatibility
    return razorpay_callback(request)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def initiate_refund(request):
    """
    Initiate refund for a payment.
    """
    serializer = InitiateRefundSerializer(data=request.data)

    if serializer.is_valid():
        transaction_id = serializer.validated_data['transaction_id']
        amount = serializer.validated_data['amount']
        reason = serializer.validated_data['reason']

        try:
            transaction = PaymentTransaction.objects.get(transaction_id=transaction_id)

            if transaction.status != 'success':
                return Response({
                    'success': False,
                    'message': 'Can only refund successful transactions'
                }, status=status.HTTP_400_BAD_REQUEST)

            if amount > transaction.amount:
                return Response({
                    'success': False,
                    'message': 'Refund amount cannot exceed transaction amount'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create refund record
            refund = PaymentRefund.objects.create(
                transaction=transaction,
                amount=amount,
                reason=reason,
                initiated_by=request.user
            )

            # Process refund based on payment method
            if transaction.payment_method == 'razorpay':
                try:
                    client = razorpay.Client(
                        auth=(
                            getattr(settings, 'RAZORPAY_KEY_ID', ''),
                            getattr(settings, 'RAZORPAY_KEY_SECRET', '')
                        )
                    )

                    refund_response = client.payment.refund(
                        transaction.gateway_payment_id,
                        {
                            'amount': int(amount * 100),  # Amount in paise
                            'speed': 'normal'
                        }
                    )

                    refund.gateway_refund_id = refund_response['id']
                    refund.status = 'processing'
                    refund.gateway_response = refund_response
                    refund.save()

                except Exception as e:
                    refund.status = 'failed'
                    refund.save()
                    return Response({
                        'success': False,
                        'message': f'Refund initiation failed: {str(e)}'
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            elif transaction.payment_method == 'cod':
                # For COD, mark as completed immediately
                refund.status = 'completed'
                refund.processed_at = timezone.now()
                refund.save()

            # Update transaction refund amount
            transaction.refund_amount += amount
            if transaction.refund_amount >= transaction.amount:
                transaction.status = 'refunded'
                transaction.refunded_at = timezone.now()
            transaction.save()

            return Response({
                'success': True,
                'message': 'Refund initiated successfully',
                'refund': RefundSerializer(refund).data
            })

        except PaymentTransaction.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Transaction not found'
            }, status=status.HTTP_404_NOT_FOUND)

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def refund_status(request, refund_id):
    """
    Get refund status.
    """
    try:
        if request.user.user_type == 'staff':
            refund = PaymentRefund.objects.get(refund_id=refund_id)
        else:
            refund = PaymentRefund.objects.get(
                refund_id=refund_id,
                transaction__user=request.user
            )

        return Response({
            'success': True,
            'refund': RefundSerializer(refund).data
        })

    except PaymentRefund.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Refund not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
def razorpay_webhook(request):
    """
    Handle Razorpay webhooks.
    """
    try:
        # Verify webhook signature
        webhook_signature = request.META.get('HTTP_X_RAZORPAY_SIGNATURE')
        webhook_secret = getattr(settings, 'RAZORPAY_WEBHOOK_SECRET', '')

        if webhook_secret:
            expected_signature = hmac.new(
                webhook_secret.encode('utf-8'),
                request.body,
                hashlib.sha256
            ).hexdigest()

            if webhook_signature != expected_signature:
                return Response({'status': 'invalid signature'}, status=400)

        # Parse webhook data
        webhook_data = json.loads(request.body)
        event_type = webhook_data.get('event')

        # Create webhook record
        webhook = PaymentWebhook.objects.create(
            source='razorpay',
            event_type=event_type,
            payload=webhook_data,
            headers=dict(request.META)
        )

        # Process webhook based on event type
        if event_type == 'payment.captured':
            # Handle successful payment
            payment_data = webhook_data.get('payload', {}).get('payment', {}).get('entity', {})
            order_id = payment_data.get('order_id')

            try:
                razorpay_payment = RazorpayPayment.objects.get(razorpay_order_id=order_id)
                transaction = razorpay_payment.transaction

                if transaction.status != 'success':
                    transaction.mark_success(payment_data)
                    razorpay_payment.webhook_verified = True
                    razorpay_payment.save()

                webhook.transaction = transaction
                webhook.processed = True
                webhook.save()

            except RazorpayPayment.DoesNotExist:
                webhook.processing_error = f"Payment not found for order_id: {order_id}"
                webhook.save()

        elif event_type == 'refund.processed':
            # Handle refund completion
            refund_data = webhook_data.get('payload', {}).get('refund', {}).get('entity', {})
            payment_id = refund_data.get('payment_id')

            try:
                transaction = PaymentTransaction.objects.get(gateway_payment_id=payment_id)
                refund = transaction.refunds.filter(
                    gateway_refund_id=refund_data.get('id')
                ).first()

                if refund:
                    refund.status = 'completed'
                    refund.processed_at = timezone.now()
                    refund.save()

                webhook.transaction = transaction
                webhook.processed = True
                webhook.save()

            except PaymentTransaction.DoesNotExist:
                webhook.processing_error = f"Transaction not found for payment_id: {payment_id}"
                webhook.save()

        return Response({'status': 'ok'})

    except Exception as e:
        return Response({'status': 'error', 'message': str(e)}, status=500)


class TransactionListView(generics.ListAPIView):
    """
    List payment transactions.
    """
    serializer_class = PaymentTransactionSerializer
    permission_classes = [IsAdminUser]

    def get_queryset(self):
        return PaymentTransaction.objects.all().select_related('user').order_by('-created_at')


class TransactionDetailView(generics.RetrieveAPIView):
    """
    Get transaction details.
    """
    serializer_class = PaymentTransactionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        if self.request.user.user_type == 'staff':
            return PaymentTransaction.objects.all()
        else:
            return PaymentTransaction.objects.filter(user=self.request.user)
