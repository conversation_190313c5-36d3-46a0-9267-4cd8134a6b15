# Systemd service file for Home Services Authentication API
# Copy this to /etc/systemd/system/home-services-auth.service

[Unit]
Description=Home Services Authentication API
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service

[Service]
Type=notify
User=www-data
Group=www-data
RuntimeDirectory=gunicorn
WorkingDirectory=/path/to/your/django_backend
Environment=PATH=/path/to/your/django_backend/venv/bin
ExecStart=/path/to/your/django_backend/venv/bin/gunicorn --config gunicorn.conf.py home_services.wsgi:application
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=on-failure
RestartSec=5

# Security settings
NoNewPrivileges=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/path/to/your/django_backend/logs /path/to/your/django_backend/media /var/log/gunicorn

# Environment variables (alternatively, use EnvironmentFile)
Environment=DJANGO_SETTINGS_MODULE=home_services.settings

[Install]
WantedBy=multi-user.target
