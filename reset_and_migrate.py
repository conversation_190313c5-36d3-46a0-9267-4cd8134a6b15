#!/usr/bin/env python
"""
Script to completely reset and properly migrate all databases.
"""
import os
import django
from django.core.management import execute_from_command_line
import shutil
import glob

def reset_and_migrate():
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
    django.setup()
    
    print("🔄 Resetting and migrating all databases...")
    
    # Step 1: Remove existing migration files (except __init__.py)
    print("\n🗑️ Cleaning up old migration files...")
    apps = ['authentication', 'catalogue', 'cart', 'coupons', 'orders', 'payments', 'providers']
    
    for app in apps:
        migrations_dir = f"{app}/migrations"
        if os.path.exists(migrations_dir):
            # Remove all migration files except __init__.py
            for file in glob.glob(f"{migrations_dir}/0*.py"):
                os.remove(file)
                print(f"  Removed {file}")
    
    # Step 2: Create fresh migrations
    print("\n📝 Creating fresh migrations...")
    for app in apps:
        try:
            print(f"  Creating migrations for {app}...")
            execute_from_command_line(['manage.py', 'makemigrations', app, '--skip-checks'])
            print(f"  ✅ {app} migrations created")
        except Exception as e:
            print(f"  ❌ {app} error: {e}")
    
    # Step 3: Apply migrations properly to each database
    print("\n🗄️ Applying migrations to databases...")
    
    # Database migration strategy
    databases = {
        'default': {
            'core_apps': ['contenttypes', 'auth', 'admin', 'sessions'],
            'custom_apps': ['authentication']
        },
        'catalogue_db': {
            'core_apps': ['contenttypes', 'auth'],
            'custom_apps': ['catalogue']
        },
        'cart_db': {
            'core_apps': ['contenttypes', 'auth'],
            'custom_apps': ['cart']
        },
        'coupons_db': {
            'core_apps': ['contenttypes', 'auth'],
            'custom_apps': ['coupons']
        },
        'orders_db': {
            'core_apps': ['contenttypes', 'auth'],
            'custom_apps': ['orders']
        },
        'payments_db': {
            'core_apps': ['contenttypes', 'auth'],
            'custom_apps': ['payments']
        },
        'providers_db': {
            'core_apps': ['contenttypes', 'auth'],
            'custom_apps': ['providers']
        }
    }
    
    for db_name, config in databases.items():
        print(f"\n📊 Migrating {db_name}...")
        
        try:
            # First, migrate core Django apps
            for app in config['core_apps']:
                print(f"  Migrating {app} to {db_name}...")
                execute_from_command_line([
                    'manage.py', 'migrate', app, 
                    f'--database={db_name}', 
                    '--skip-checks'
                ])
            
            # Then migrate custom apps
            for app in config['custom_apps']:
                print(f"  Migrating {app} to {db_name}...")
                execute_from_command_line([
                    'manage.py', 'migrate', app, 
                    f'--database={db_name}', 
                    '--skip-checks'
                ])
            
            print(f"  ✅ {db_name} migrated successfully")
            
        except Exception as e:
            print(f"  ❌ {db_name} error: {e}")
            # Continue with other databases
            continue
    
    print("\n🎉 Database reset and migration completed!")
    print("\n📋 Next steps:")
    print("1. Run: python manage.py createsuperuser")
    print("2. Run: python manage.py runserver")
    print("3. Visit: http://127.0.0.1:8000/admin/")

if __name__ == '__main__':
    reset_and_migrate()
