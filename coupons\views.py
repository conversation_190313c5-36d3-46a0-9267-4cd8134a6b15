from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAd<PERSON>U<PERSON>, AllowAny
from django.shortcuts import get_object_or_404
from django.db import transaction
from decimal import Decimal
from .models import Coupon, UsedCoupon, CouponApplication
from .serializers import (
    CouponSerializer, CouponListSerializer, CouponCreateUpdateSerializer,
    ApplyCouponSerializer, CouponValidationSerializer, UsedCouponSerializer,
    CouponDiscountCalculationSerializer, SequentialCouponApplicationSerializer
)


class CouponListCreateView(generics.ListCreateAPIView):
    """
    List all coupons or create a new coupon.
    GET: Public access for listing active coupons
    POST: Admin only for creation
    """
    queryset = Coupon.objects.all()
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return CouponCreateUpdateSerializer
        return CouponListSerializer
    
    def get_permissions(self):
        if self.request.method == 'POST':
            return [IsAdminUser()]
        return [AllowAny()]
    
    def get_queryset(self):
        queryset = Coupon.objects.all()
        
        # Filter by active status for non-admin users
        if not self.request.user.is_staff:
            queryset = queryset.filter(is_active=True)
        
        # Filter by validity
        show_valid_only = self.request.query_params.get('valid_only', 'false').lower() == 'true'
        if show_valid_only:
            # This would need to be implemented with a custom manager or filter
            # For now, we'll filter in Python (not ideal for large datasets)
            valid_coupons = [coupon.id for coupon in queryset if coupon.is_valid()]
            queryset = queryset.filter(id__in=valid_coupons)
        
        return queryset.order_by('-created_at')


class CouponDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a coupon.
    GET: Public access for active coupons
    PUT/PATCH/DELETE: Admin only
    """
    queryset = Coupon.objects.all()
    lookup_field = 'code'
    
    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return CouponCreateUpdateSerializer
        return CouponSerializer
    
    def get_permissions(self):
        if self.request.method == 'GET':
            return [AllowAny()]
        return [IsAdminUser()]
    
    def get_object(self):
        code = self.kwargs['code'].upper()
        if self.request.user.is_staff:
            return get_object_or_404(Coupon, code=code)
        else:
            return get_object_or_404(Coupon, code=code, is_active=True)


@api_view(['POST'])
@permission_classes([AllowAny])
def validate_coupon(request):
    """
    Validate a coupon code without applying it.
    Returns discount calculation and validity information.
    """
    serializer = CouponValidationSerializer(data=request.data)
    
    if serializer.is_valid():
        coupon_code = serializer.validated_data['coupon_code']
        cart_total = serializer.validated_data.get('cart_total', Decimal('0.00'))
        user_id = serializer.validated_data.get('user_id')
        
        try:
            coupon = Coupon.objects.get(code=coupon_code)
            
            # Check if coupon is valid
            if not coupon.is_valid():
                return Response({
                    'valid': False,
                    'message': 'Coupon is expired or inactive',
                    'coupon': None
                }, status=status.HTTP_200_OK)
            
            # Check user usage limits if user provided
            if user_id:
                from authentication.models import User
                try:
                    user = User.objects.get(id=user_id)
                    if not coupon.can_be_used_by_user(user):
                        return Response({
                            'valid': False,
                            'message': 'You have already used this coupon the maximum number of times',
                            'coupon': None
                        }, status=status.HTTP_200_OK)
                except User.DoesNotExist:
                    pass
            
            # Calculate discount
            discount_amount = coupon.calculate_discount(cart_total)
            final_amount = cart_total - discount_amount
            
            # Check minimum cart value
            if cart_total < coupon.min_cart_value:
                return Response({
                    'valid': False,
                    'message': f'Minimum cart value of ₹{coupon.min_cart_value} required',
                    'coupon': CouponListSerializer(coupon).data,
                    'discount_amount': Decimal('0.00'),
                    'final_amount': cart_total
                }, status=status.HTTP_200_OK)
            
            return Response({
                'valid': True,
                'message': 'Coupon is valid',
                'coupon': CouponListSerializer(coupon).data,
                'discount_amount': discount_amount,
                'final_amount': final_amount,
                'savings': discount_amount
            }, status=status.HTTP_200_OK)
            
        except Coupon.DoesNotExist:
            return Response({
                'valid': False,
                'message': 'Invalid coupon code',
                'coupon': None
            }, status=status.HTTP_200_OK)
    
    return Response({
        'valid': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def apply_coupon(request):
    """
    Apply a single coupon to cart.
    This integrates with the Cart service.
    """
    serializer = ApplyCouponSerializer(data=request.data)
    
    if serializer.is_valid():
        coupon_code = serializer.validated_data['coupon_code']
        cart_total = serializer.validated_data['cart_total']
        
        try:
            coupon = Coupon.objects.get(code=coupon_code)
            
            # Validate coupon
            if not coupon.is_valid():
                return Response({
                    'success': False,
                    'message': 'Coupon is expired or inactive'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Check minimum cart value
            if cart_total < coupon.min_cart_value:
                return Response({
                    'success': False,
                    'message': f'Minimum cart value of ₹{coupon.min_cart_value} required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Check user usage limits
            user = request.user if request.user.is_authenticated else None
            if user and not coupon.can_be_used_by_user(user):
                return Response({
                    'success': False,
                    'message': 'You have already used this coupon the maximum number of times'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Calculate discount
            discount_amount = coupon.calculate_discount(cart_total)
            final_amount = cart_total - discount_amount
            
            return Response({
                'success': True,
                'message': f'Coupon {coupon_code} applied successfully',
                'coupon': CouponListSerializer(coupon).data,
                'original_amount': cart_total,
                'discount_amount': discount_amount,
                'final_amount': final_amount,
                'savings': discount_amount
            }, status=status.HTTP_200_OK)
            
        except Coupon.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Invalid coupon code'
            }, status=status.HTTP_400_BAD_REQUEST)
    
    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def apply_sequential_coupons(request):
    """
    Apply multiple coupons sequentially as per requirements.
    Each coupon is applied to the updated cart amount after previous discounts.
    """
    serializer = SequentialCouponApplicationSerializer(data=request.data)

    if serializer.is_valid():
        cart_id = serializer.validated_data['cart_id']
        coupon_codes = serializer.validated_data['coupon_codes']
        current_amount = serializer.validated_data['initial_cart_total']

        applications = []
        total_discount = Decimal('0.00')
        user = request.user if request.user.is_authenticated else None

        try:
            with transaction.atomic():
                # Clear any existing applications for this cart
                CouponApplication.objects.filter(cart_id=cart_id).delete()

                for order, coupon_code in enumerate(coupon_codes, 1):
                    coupon = Coupon.objects.get(code=coupon_code)

                    # Validate coupon
                    if not coupon.is_valid():
                        return Response({
                            'success': False,
                            'message': f'Coupon {coupon_code} is expired or inactive'
                        }, status=status.HTTP_400_BAD_REQUEST)

                    # Check user usage limits
                    if user and not coupon.can_be_used_by_user(user):
                        return Response({
                            'success': False,
                            'message': f'You have already used coupon {coupon_code} the maximum number of times'
                        }, status=status.HTTP_400_BAD_REQUEST)

                    # Check minimum cart value
                    if current_amount < coupon.min_cart_value:
                        return Response({
                            'success': False,
                            'message': f'Coupon {coupon_code} requires minimum cart value of ₹{coupon.min_cart_value}'
                        }, status=status.HTTP_400_BAD_REQUEST)

                    # Calculate discount on current amount
                    discount_amount = coupon.calculate_discount(current_amount)
                    amount_before = current_amount
                    current_amount -= discount_amount
                    total_discount += discount_amount

                    # Record the application
                    application = CouponApplication.objects.create(
                        cart_id=cart_id,
                        coupon=coupon,
                        order_applied=order,
                        cart_amount_before=amount_before,
                        discount_amount=discount_amount,
                        cart_amount_after=current_amount
                    )
                    applications.append(application)

                return Response({
                    'success': True,
                    'message': f'Applied {len(coupon_codes)} coupons sequentially',
                    'cart_id': cart_id,
                    'initial_amount': serializer.validated_data['initial_cart_total'],
                    'final_amount': current_amount,
                    'total_discount': total_discount,
                    'applications': [
                        {
                            'order': app.order_applied,
                            'coupon_code': app.coupon.code,
                            'amount_before': app.cart_amount_before,
                            'discount_amount': app.discount_amount,
                            'amount_after': app.cart_amount_after
                        }
                        for app in applications
                    ]
                }, status=status.HTTP_200_OK)

        except Coupon.DoesNotExist as e:
            return Response({
                'success': False,
                'message': f'Invalid coupon code in the list'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error applying coupons: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def redeem_coupon(request):
    """
    Mark a coupon as used after successful order completion.
    Called by Order Management Service.
    """
    coupon_code = request.data.get('coupon_code')
    cart_total = request.data.get('cart_total')
    discount_amount = request.data.get('discount_amount')

    if not all([coupon_code, cart_total, discount_amount]):
        return Response({
            'success': False,
            'message': 'Missing required fields: coupon_code, cart_total, discount_amount'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        coupon = Coupon.objects.get(code=coupon_code.upper())

        # Create usage record
        used_coupon = UsedCoupon.objects.create(
            user=request.user if request.user.is_authenticated else None,
            coupon=coupon,
            cart_total=Decimal(str(cart_total)),
            discount_amount=Decimal(str(discount_amount)),
            session_key=request.session.session_key if not request.user.is_authenticated else None
        )

        return Response({
            'success': True,
            'message': f'Coupon {coupon_code} redeemed successfully',
            'usage_record': UsedCouponSerializer(used_coupon).data
        }, status=status.HTTP_201_CREATED)

    except Coupon.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Invalid coupon code'
        }, status=status.HTTP_400_BAD_REQUEST)


class UsedCouponListView(generics.ListAPIView):
    """
    List coupon usage history.
    Admin can see all, users can see their own.
    """
    serializer_class = UsedCouponSerializer

    def get_permissions(self):
        return [IsAdminUser()] if self.request.query_params.get('all') else [AllowAny()]

    def get_queryset(self):
        if self.request.user.is_staff and self.request.query_params.get('all'):
            return UsedCoupon.objects.all().order_by('-used_at')
        elif self.request.user.is_authenticated:
            return UsedCoupon.objects.filter(user=self.request.user).order_by('-used_at')
        else:
            # For anonymous users, show usage for current session
            session_key = self.request.session.session_key
            if session_key:
                return UsedCoupon.objects.filter(session_key=session_key).order_by('-used_at')
            return UsedCoupon.objects.none()
