from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenRefreshView
from django.contrib.auth import authenticate, get_user_model
from django.core.cache import cache
from django.utils import timezone
from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from drf_spectacular.utils import extend_schema, OpenApiExample
from drf_spectacular.openapi import OpenApiParameter
import logging

from .models import Address, FailedLoginAttempt
from .serializers import (
    UserRegistrationSerializer, MobileRegistrationSerializer,
    EmailLoginSerializer, MobileLoginSerializer, SendOTPSerializer,
    VerifyOTPSerializer, UserProfileSerializer, UserUpdateSerializer,
    PasswordResetRequestSerializer, PasswordResetConfirmSerializer,
    AddressSerializer, FailedLoginAttemptSerializer, ChangePasswordSerializer
)
from .utils import OTPService, RateLimitService, SecurityService
from .backends import EmailBackend, MobileBackend
from django.db import connection
from django.http import JsonResponse

User = get_user_model()
logger = logging.getLogger(__name__)


class RegisterView(APIView):
    """
    User registration endpoint
    """
    permission_classes = [permissions.AllowAny]

    @method_decorator(ratelimit(key='ip', rate='5/h', method='POST', block=True))
    def post(self, request):
        # Check if IP is blocked
        ip_address = SecurityService.get_client_ip(request)
        if SecurityService.is_ip_blocked(ip_address):
            return Response(
                {'error': 'IP address is temporarily blocked'},
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )

        serializer = UserRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            try:
                user = serializer.save()

                # Generate tokens
                refresh = RefreshToken.for_user(user)

                return Response({
                    'message': 'User registered successfully',
                    'user': UserProfileSerializer(user).data,
                    'tokens': {
                        'refresh': str(refresh),
                        'access': str(refresh.access_token),
                    }
                }, status=status.HTTP_201_CREATED)

            except Exception as e:
                logger.error(f"Registration error: {str(e)}")
                return Response(
                    {'error': 'Registration failed'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class MobileRegisterView(APIView):
    """
    Mobile-based registration for customers and providers
    """
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="Register Customer/Provider with Mobile Number",
        description="Register a new customer or provider user using mobile number. OTP will be sent for verification.",
        request=MobileRegistrationSerializer,
        responses={
            201: OpenApiExample(
                "Registration Successful",
                value={
                    "message": "User registered successfully. OTP sent for verification.",
                    "user_id": 123,
                    "mobile_number": "+************",
                    "requires_verification": True
                }
            ),
            400: OpenApiExample(
                "Validation Error",
                value={
                    "mobile_number": ["User with this mobile number already exists"],
                    "name": ["This field is required"]
                }
            )
        },
        examples=[
            OpenApiExample(
                "Customer Registration",
                value={
                    "mobile_number": "+************",
                    "name": "John Doe",
                    "user_type": "CUSTOMER"
                }
            ),
            OpenApiExample(
                "Provider Registration",
                value={
                    "mobile_number": "+919876543211",
                    "name": "Jane Smith",
                    "user_type": "PROVIDER"
                }
            )
        ]
    )

    @method_decorator(ratelimit(key='ip', rate='5/h', method='POST', block=True))
    def post(self, request):
        ip_address = SecurityService.get_client_ip(request)
        if SecurityService.is_ip_blocked(ip_address):
            return Response(
                {'error': 'IP address is temporarily blocked'},
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )

        serializer = MobileRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            try:
                # Create user (unverified)
                user = serializer.save()

                # Send OTP for verification
                mobile_number = user.mobile_number
                success, otp = OTPService.send_otp(mobile_number)

                if success:
                    return Response({
                        'message': 'User registered successfully. OTP sent for verification.',
                        'user_id': user.id,
                        'mobile_number': mobile_number,
                        'requires_verification': True
                    }, status=status.HTTP_201_CREATED)
                else:
                    # Delete user if OTP sending failed
                    user.delete()
                    return Response(
                        {'error': 'Failed to send OTP. Please try again.'},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )

            except Exception as e:
                logger.error(f"Mobile registration error: {str(e)}")
                return Response(
                    {'error': 'Registration failed'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class EmailLoginView(APIView):
    """
    Email-based login for staff users
    """
    permission_classes = [permissions.AllowAny]

    @method_decorator(ratelimit(key='ip', rate='5/h', method='POST', block=True))
    def post(self, request):
        ip_address = SecurityService.get_client_ip(request)
        if SecurityService.is_ip_blocked(ip_address):
            return Response(
                {'error': 'IP address is temporarily blocked'},
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )

        serializer = EmailLoginSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            password = serializer.validated_data['password']

            # Authenticate using email backend
            backend = EmailBackend()
            user = backend.authenticate(request, username=email, password=password)

            if user:
                # Generate tokens
                refresh = RefreshToken.for_user(user)

                # Update last login
                user.last_login = timezone.now()
                user.save(update_fields=['last_login'])

                return Response({
                    'message': 'Login successful',
                    'user': UserProfileSerializer(user).data,
                    'tokens': {
                        'refresh': str(refresh),
                        'access': str(refresh.access_token),
                    }
                }, status=status.HTTP_200_OK)
            else:
                return Response(
                    {'error': 'Invalid credentials or account locked'},
                    status=status.HTTP_401_UNAUTHORIZED
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class MobileLoginView(APIView):
    """
    Mobile-based login for customers and providers
    """
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="Login with Mobile Number and OTP",
        description="Login customer or provider user using mobile number and OTP. Returns JWT tokens on successful authentication.",
        request=MobileLoginSerializer,
        responses={
            200: OpenApiExample(
                "Login Successful",
                value={
                    "message": "Login successful",
                    "user": {
                        "id": 123,
                        "mobile_number": "+************",
                        "name": "John Doe",
                        "user_type": "CUSTOMER",
                        "is_verified": True,
                        "date_joined": "2025-01-27T10:30:00Z"
                    },
                    "tokens": {
                        "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                        "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
                    }
                }
            ),
            401: OpenApiExample(
                "Authentication Failed",
                value={
                    "error": "Invalid OTP or account locked"
                }
            ),
            400: OpenApiExample(
                "Validation Error",
                value={
                    "mobile_number": ["This field is required."],
                    "otp": ["This field is required."]
                }
            )
        },
        examples=[
            OpenApiExample(
                "Mobile Login Request",
                value={
                    "mobile_number": "+************",
                    "otp": "123456"
                },
                request_only=True
            )
        ]
    )

    @method_decorator(ratelimit(key='ip', rate='5/h', method='POST', block=True))
    def post(self, request):
        ip_address = SecurityService.get_client_ip(request)
        if SecurityService.is_ip_blocked(ip_address):
            return Response(
                {'error': 'IP address is temporarily blocked'},
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )

        serializer = MobileLoginSerializer(data=request.data)
        if serializer.is_valid():
            mobile_number = serializer.validated_data['mobile_number']
            otp = serializer.validated_data['otp']

            # Authenticate using mobile backend
            backend = MobileBackend()
            user = backend.authenticate(request, mobile_number=mobile_number, otp=otp)

            if user:
                # Generate tokens
                refresh = RefreshToken.for_user(user)

                # Update last login
                user.last_login = timezone.now()
                user.save(update_fields=['last_login'])

                return Response({
                    'message': 'Login successful',
                    'user': UserProfileSerializer(user).data,
                    'tokens': {
                        'refresh': str(refresh),
                        'access': str(refresh.access_token),
                    }
                }, status=status.HTTP_200_OK)
            else:
                return Response(
                    {'error': 'Invalid OTP or account locked'},
                    status=status.HTTP_401_UNAUTHORIZED
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SendOTPView(APIView):
    """
    Send OTP to mobile number
    """
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="Send OTP to Mobile Number",
        description="Send a 6-digit OTP to the specified mobile number for verification via SMS.",
        request=SendOTPSerializer,
        responses={
            200: OpenApiExample(
                "OTP Sent Successfully",
                value={
                    "message": "OTP sent successfully",
                    "mobile_number": "+************"
                }
            ),
            400: OpenApiExample(
                "Validation Error",
                value={
                    "mobile_number": ["Enter a valid Indian mobile number. Format: +91XXXXXXXXXX"]
                }
            ),
            500: OpenApiExample(
                "Server Error",
                value={
                    "error": "Failed to send OTP. Please try again."
                }
            )
        },
        examples=[
            OpenApiExample(
                "Send OTP Request",
                value={
                    "mobile_number": "+************"
                },
                request_only=True
            )
        ]
    )

    @method_decorator(ratelimit(key='ip', rate='3/h', method='POST', block=True))
    def post(self, request):
        ip_address = SecurityService.get_client_ip(request)
        if SecurityService.is_ip_blocked(ip_address):
            return Response(
                {'error': 'IP address is temporarily blocked'},
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )

        serializer = SendOTPSerializer(data=request.data)
        if serializer.is_valid():
            mobile_number = serializer.validated_data['mobile_number']

            # Check rate limit for this mobile number
            rate_limit_key = f"otp_send_{mobile_number}"
            if RateLimitService.is_rate_limited(rate_limit_key, limit=3, window_seconds=3600):
                return Response(
                    {'error': 'Too many OTP requests. Please try again later.'},
                    status=status.HTTP_429_TOO_MANY_REQUESTS
                )

            # Send OTP
            success, otp = OTPService.send_otp(mobile_number)

            if success:
                return Response({
                    'message': 'OTP sent successfully',
                    'mobile_number': mobile_number
                }, status=status.HTTP_200_OK)
            else:
                return Response(
                    {'error': 'Failed to send OTP. Please try again.'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class VerifyOTPView(APIView):
    """
    Verify OTP for mobile number
    """
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="Verify OTP for Mobile Number",
        description="Verify the 6-digit OTP sent to the mobile number.",
        request=VerifyOTPSerializer,
        responses={
            200: OpenApiExample(
                "OTP Verified Successfully",
                value={
                    "message": "OTP verified successfully",
                    "user_verified": True
                }
            ),
            400: OpenApiExample(
                "Invalid OTP",
                value={
                    "error": "Invalid or expired OTP"
                }
            ),
            400: OpenApiExample(
                "Validation Error",
                value={
                    "mobile_number": ["This field is required."],
                    "otp": ["This field is required."]
                }
            )
        },
        examples=[
            OpenApiExample(
                "Verify OTP Request",
                value={
                    "mobile_number": "+************",
                    "otp": "123456"
                },
                request_only=True
            )
        ]
    )
    def post(self, request):
        serializer = VerifyOTPSerializer(data=request.data)
        if serializer.is_valid():
            mobile_number = serializer.validated_data['mobile_number']
            otp = serializer.validated_data['otp']

            # Verify OTP
            if OTPService.verify_otp(mobile_number, otp):
                # Mark user as verified if exists
                try:
                    user = User.objects.get(mobile_number=mobile_number)
                    user.is_verified = True
                    user.save(update_fields=['is_verified'])

                    return Response({
                        'message': 'OTP verified successfully',
                        'user_verified': True
                    }, status=status.HTTP_200_OK)
                except User.DoesNotExist:
                    return Response({
                        'message': 'OTP verified successfully',
                        'user_verified': False
                    }, status=status.HTTP_200_OK)
            else:
                return Response(
                    {'error': 'Invalid or expired OTP'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ResendOTPView(APIView):
    """
    Resend OTP to mobile number
    """
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="Resend OTP to Mobile Number",
        description="Resend OTP to the specified mobile number via voice call.",
        request=SendOTPSerializer,
        responses={
            200: OpenApiExample(
                "OTP Resent Successfully",
                value={
                    "message": "OTP resent successfully",
                    "mobile_number": "+************",
                    "retry_type": "voice"
                }
            ),
            400: OpenApiExample(
                "Invalid Mobile Number",
                value={
                    "mobile_number": ["Enter a valid Indian mobile number"]
                }
            )
        },
        examples=[
            OpenApiExample(
                "Resend OTP Request",
                value={
                    "mobile_number": "+************"
                }
            )
        ]
    )
    def post(self, request):
        serializer = SendOTPSerializer(data=request.data)
        if serializer.is_valid():
            mobile_number = serializer.validated_data['mobile_number']

            # Resend OTP via voice
            success = OTPService.resend_otp(mobile_number, retry_type='voice')

            if success:
                return Response({
                    'message': 'OTP resent successfully',
                    'mobile_number': mobile_number,
                    'retry_type': 'voice'
                }, status=status.HTTP_200_OK)
            else:
                return Response(
                    {'error': 'Failed to resend OTP. Please try again.'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserProfileView(APIView):
    """
    Get and update user profile
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        serializer = UserProfileSerializer(request.user)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request):
        serializer = UserUpdateSerializer(request.user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response({
                'message': 'Profile updated successfully',
                'user': UserProfileSerializer(request.user).data
            }, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ChangePasswordView(APIView):
    """
    Change user password
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        # Only staff users can change password
        if request.user.user_type != 'STAFF':
            return Response(
                {'error': 'Password change is only available for staff users'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = ChangePasswordSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            # Set new password
            request.user.set_password(serializer.validated_data['new_password'])
            request.user.save()

            return Response({
                'message': 'Password changed successfully'
            }, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AddressListCreateView(APIView):
    """
    List and create user addresses
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        addresses = Address.objects.filter(user=request.user)
        serializer = AddressSerializer(addresses, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = AddressSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(user=request.user)
            return Response({
                'message': 'Address created successfully',
                'address': serializer.data
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AddressDetailView(APIView):
    """
    Retrieve, update, and delete user addresses
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self, pk, user):
        try:
            return Address.objects.get(pk=pk, user=user)
        except Address.DoesNotExist:
            return None

    def get(self, request, pk):
        address = self.get_object(pk, request.user)
        if not address:
            return Response(
                {'error': 'Address not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        serializer = AddressSerializer(address)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, pk):
        address = self.get_object(pk, request.user)
        if not address:
            return Response(
                {'error': 'Address not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        serializer = AddressSerializer(address, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response({
                'message': 'Address updated successfully',
                'address': serializer.data
            }, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        address = self.get_object(pk, request.user)
        if not address:
            return Response(
                {'error': 'Address not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        address.delete()
        return Response({
            'message': 'Address deleted successfully'
        }, status=status.HTTP_204_NO_CONTENT)


class LogoutView(APIView):
    """
    Logout user by blacklisting refresh token
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            refresh_token = request.data.get('refresh')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()

            return Response({
                'message': 'Logged out successfully'
            }, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Logout error: {str(e)}")
            return Response({
                'message': 'Logged out successfully'
            }, status=status.HTTP_200_OK)


# Admin Views
class AdminFailedAttemptsView(APIView):
    """
    Admin view for failed login attempts
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        # Only staff users can access
        if not request.user.is_staff:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        attempts = FailedLoginAttempt.objects.all()[:100]  # Last 100 attempts
        serializer = FailedLoginAttemptSerializer(attempts, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class AdminUnlockAccountView(APIView):
    """
    Admin view to unlock user accounts
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        if not request.user.is_staff:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        user_id = request.data.get('user_id')
        if not user_id:
            return Response(
                {'error': 'User ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            user = User.objects.get(id=user_id)
            user.unlock_account()

            return Response({
                'message': f'Account unlocked for user: {user.name}'
            }, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'},
                status=status.HTTP_404_NOT_FOUND
            )


class AdminResetRateLimitView(APIView):
    """
    Admin view to reset rate limits
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        if not request.user.is_staff:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        key = request.data.get('key')
        if not key:
            return Response(
                {'error': 'Rate limit key is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        RateLimitService.reset_rate_limit(key)

        return Response({
            'message': f'Rate limit reset for key: {key}'
        }, status=status.HTTP_200_OK)


class HealthCheckView(APIView):
    """
    Health check endpoint for production monitoring
    """
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        """
        Perform basic health checks
        """
        health_status = {
            'status': 'healthy',
            'timestamp': timezone.now().isoformat(),
            'checks': {}
        }

        # Database check
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                health_status['checks']['database'] = 'healthy'
        except Exception as e:
            health_status['checks']['database'] = f'unhealthy: {str(e)}'
            health_status['status'] = 'unhealthy'

        # Cache check
        try:
            cache.set('health_check', 'test', timeout=10)
            if cache.get('health_check') == 'test':
                health_status['checks']['cache'] = 'healthy'
                cache.delete('health_check')
            else:
                health_status['checks']['cache'] = 'unhealthy: cache not working'
                health_status['status'] = 'unhealthy'
        except Exception as e:
            health_status['checks']['cache'] = f'unhealthy: {str(e)}'
            health_status['status'] = 'unhealthy'

        # Return appropriate status code
        status_code = status.HTTP_200_OK if health_status['status'] == 'healthy' else status.HTTP_503_SERVICE_UNAVAILABLE

        return Response(health_status, status=status_code)


class AdminUserStatsView(APIView):
    """
    Admin view for user statistics
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        if not request.user.is_staff:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get user statistics
        total_users = User.objects.count()
        customers = User.objects.filter(user_type='CUSTOMER').count()
        providers = User.objects.filter(user_type='PROVIDER').count()
        staff = User.objects.filter(user_type='STAFF').count()
        verified_users = User.objects.filter(is_verified=True).count()
        locked_accounts = User.objects.filter(is_locked=True).count()

        # Recent failed attempts
        recent_attempts = FailedLoginAttempt.objects.filter(
            timestamp__gte=timezone.now() - timezone.timedelta(hours=24)
        ).count()

        return Response({
            'total_users': total_users,
            'user_types': {
                'customers': customers,
                'providers': providers,
                'staff': staff
            },
            'verified_users': verified_users,
            'locked_accounts': locked_accounts,
            'recent_failed_attempts': recent_attempts
        }, status=status.HTTP_200_OK)
