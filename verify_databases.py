#!/usr/bin/env python
"""
Script to verify database connections and table creation.
"""
import os
import django
from django.db import connections
from django.core.management import execute_from_command_line

def verify_databases():
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
    django.setup()
    
    print("🔍 Verifying Database Connections...")
    
    databases = {
        'default': 'home_services_auth',
        'catalogue_db': 'home_services_catalogue',
        'cart_db': 'home_services_cart',
        'coupons_db': 'home_services_coupons',
        'orders_db': 'home_services_orders',
        'payments_db': 'home_services_payments',
        'providers_db': 'home_services_providers'
    }
    
    for db_alias, db_name in databases.items():
        print(f"\n📊 Testing {db_alias} ({db_name})...")
        try:
            connection = connections[db_alias]
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                if result:
                    print(f"  ✅ Connection successful")
                    
                    # Check if tables exist
                    cursor.execute("""
                        SELECT table_name 
                        FROM information_schema.tables 
                        WHERE table_schema = 'public'
                        ORDER BY table_name
                    """)
                    tables = cursor.fetchall()
                    if tables:
                        print(f"  📋 Tables found: {len(tables)}")
                        for table in tables[:5]:  # Show first 5 tables
                            print(f"    - {table[0]}")
                        if len(tables) > 5:
                            print(f"    ... and {len(tables) - 5} more")
                    else:
                        print(f"  ⚠️ No tables found - migration needed")
                        
        except Exception as e:
            print(f"  ❌ Connection failed: {e}")
    
    print("\n🎯 Database verification completed!")

if __name__ == '__main__':
    verify_databases()
