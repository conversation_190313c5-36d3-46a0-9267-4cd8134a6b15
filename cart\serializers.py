from rest_framework import serializers
from django.db import transaction
from catalogue.models import Service, Category
from .models import Cart, CartItem


class CartItemSerializer(serializers.ModelSerializer):
    """
    Serializer for CartItem with service details and calculations.
    """
    service_title = serializers.CharField(source='service.title', read_only=True)
    service_slug = serializers.CharField(source='service.slug', read_only=True)
    service_image = serializers.ImageField(source='service.image', read_only=True)
    service_category = serializers.CharField(source='service.category.name', read_only=True)
    current_service_price = serializers.DecimalField(
        source='service.get_current_price', 
        max_digits=10, 
        decimal_places=2, 
        read_only=True
    )
    total_price = serializers.ReadOnlyField(source='get_total_price')
    savings = serializers.ReadOnlyField(source='get_savings')

    class Meta:
        model = CartItem
        fields = [
            'id', 'service', 'service_title', 'service_slug', 
            'service_image', 'service_category', 'quantity', 
            'price_at_add', 'discount_at_add', 'current_service_price',
            'total_price', 'savings', 'added_at', 'updated_at'
        ]
        read_only_fields = ['price_at_add', 'discount_at_add', 'added_at', 'updated_at']


class CartSerializer(serializers.ModelSerializer):
    """
    Serializer for Cart with items and totals.
    """
    items = CartItemSerializer(many=True, read_only=True)
    items_count = serializers.ReadOnlyField(source='get_items_count')
    unique_services_count = serializers.ReadOnlyField(source='get_unique_services_count')

    class Meta:
        model = Cart
        fields = [
            'id', 'user', 'session_key', 'created_at', 'updated_at',
            'is_active', 'sub_total', 'tax_amount', 'discount_amount',
            'minimum_order_fee_applied', 'total_amount', 'coupon_code_applied',
            'items', 'items_count', 'unique_services_count'
        ]
        read_only_fields = [
            'created_at', 'updated_at', 'sub_total', 'tax_amount',
            'discount_amount', 'minimum_order_fee_applied', 'total_amount'
        ]


class AddToCartSerializer(serializers.Serializer):
    """
    Serializer for adding items to cart with validation.
    """
    service_id = serializers.IntegerField()
    quantity = serializers.IntegerField(min_value=1, default=1)

    def validate_service_id(self, value):
        """Validate that service exists and is active"""
        try:
            service = Service.objects.get(id=value, is_active=True)
            return value
        except Service.DoesNotExist:
            raise serializers.ValidationError("Service not found or inactive.")

    def validate(self, data):
        """
        Validate that all services in cart belong to same top-level category.
        This is a key business rule from the requirements.
        """
        service_id = data['service_id']
        service = Service.objects.get(id=service_id)
        
        # Get the cart from context
        cart = self.context.get('cart')
        if not cart:
            return data
        
        # Get the top-level category of the new service
        new_service_root_category = service.category.get_root()
        
        # Check existing cart items
        existing_items = cart.items.all()
        if existing_items.exists():
            # Get the top-level category of the first existing item
            first_item_service = existing_items.first().service
            first_item_root_category = first_item_service.category.get_root()
            
            # Compare top-level categories
            if new_service_root_category != first_item_root_category:
                raise serializers.ValidationError(
                    f"All services in the cart must belong to the same top-level category. "
                    f"Current cart contains services from '{first_item_root_category.name}' "
                    f"but you're trying to add a service from '{new_service_root_category.name}'."
                )
        
        return data


class UpdateCartItemSerializer(serializers.Serializer):
    """
    Serializer for updating cart item quantity.
    """
    quantity = serializers.IntegerField(min_value=1)


class ApplyCouponSerializer(serializers.Serializer):
    """
    Serializer for applying coupon codes to cart.
    """
    coupon_code = serializers.CharField(max_length=50)

    def validate_coupon_code(self, value):
        """Validate coupon code format"""
        if not value.strip():
            raise serializers.ValidationError("Coupon code cannot be empty.")
        return value.strip().upper()


class CartSummarySerializer(serializers.ModelSerializer):
    """
    Simplified cart serializer for summary views.
    """
    items_count = serializers.ReadOnlyField(source='get_items_count')
    unique_services_count = serializers.ReadOnlyField(source='get_unique_services_count')

    class Meta:
        model = Cart
        fields = [
            'id', 'sub_total', 'tax_amount', 'discount_amount',
            'minimum_order_fee_applied', 'total_amount', 'coupon_code_applied',
            'items_count', 'unique_services_count', 'updated_at'
        ]


class CartItemCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating cart items with automatic price capture.
    """
    class Meta:
        model = CartItem
        fields = ['service', 'quantity']

    def create(self, validated_data):
        """
        Create cart item with current service price and discount.
        """
        service = validated_data['service']
        cart = validated_data['cart']
        quantity = validated_data['quantity']
        
        # Get current price and any applicable discount
        current_price = service.get_current_price()
        discount = service.base_price - current_price if service.discount_price else Decimal('0.00')
        
        # Check if item already exists in cart
        existing_item = CartItem.objects.filter(cart=cart, service=service).first()
        
        if existing_item:
            # Update quantity of existing item
            existing_item.quantity += quantity
            existing_item.save()
            return existing_item
        else:
            # Create new cart item
            cart_item = CartItem.objects.create(
                cart=cart,
                service=service,
                quantity=quantity,
                price_at_add=service.base_price,
                discount_at_add=discount
            )
            return cart_item


class CheckoutValidationSerializer(serializers.Serializer):
    """
    Serializer for validating cart before checkout.
    """
    address_id = serializers.IntegerField(required=False)
    payment_method = serializers.ChoiceField(
        choices=[('razorpay', 'Razorpay'), ('cod', 'Cash on Delivery')],
        default='razorpay'
    )
    
    def validate(self, data):
        """Validate cart is ready for checkout"""
        cart = self.context.get('cart')
        
        if not cart or not cart.items.exists():
            raise serializers.ValidationError("Cart is empty.")
        
        if cart.total_amount <= 0:
            raise serializers.ValidationError("Cart total must be greater than zero.")
        
        return data
