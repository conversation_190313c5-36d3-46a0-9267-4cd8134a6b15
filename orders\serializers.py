from rest_framework import serializers
from django.db import transaction
from decimal import Decimal
from .models import Order, OrderItem, OrderStatusHistory, OrderCancellation, OrderReschedule
from catalogue.models import Service


class OrderItemSerializer(serializers.ModelSerializer):
    """
    Serializer for OrderItem with service details.
    """
    service_title = serializers.CharField(source='service.title', read_only=True)
    service_slug = serializers.CharField(source='service.slug', read_only=True)
    service_image = serializers.ImageField(source='service.image', read_only=True)
    service_category = serializers.CharField(source='service.category.name', read_only=True)

    class Meta:
        model = OrderItem
        fields = [
            'id', 'service', 'service_title', 'service_slug', 
            'service_image', 'service_category', 'quantity',
            'unit_price', 'discount_per_unit', 'total_price',
            'estimated_duration', 'special_instructions'
        ]


class OrderSerializer(serializers.ModelSerializer):
    """
    Comprehensive serializer for Order with all related data.
    """
    items = OrderItemSerializer(many=True, read_only=True)
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)
    customer_mobile = serializers.CharField(source='customer.mobile', read_only=True)
    assigned_provider_name = serializers.CharField(
        source='assigned_provider.get_full_name', 
        read_only=True
    )
    assigned_provider_mobile = serializers.CharField(
        source='assigned_provider.mobile', 
        read_only=True
    )
    items_count = serializers.SerializerMethodField()
    can_cancel = serializers.ReadOnlyField(source='can_be_cancelled')
    can_reschedule = serializers.ReadOnlyField(source='can_be_rescheduled')

    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'customer', 'customer_name', 'customer_mobile',
            'status', 'payment_status', 'payment_method', 'subtotal', 'tax_amount',
            'discount_amount', 'minimum_order_fee', 'total_amount', 'coupon_code',
            'coupon_discount', 'delivery_address', 'assigned_provider',
            'assigned_provider_name', 'assigned_provider_mobile', 'scheduled_date',
            'scheduled_time_slot', 'payment_id', 'customer_notes', 'admin_notes',
            'items', 'items_count', 'can_cancel', 'can_reschedule',
            'created_at', 'updated_at', 'confirmed_at', 'completed_at', 'cancelled_at'
        ]
        read_only_fields = [
            'id', 'order_number', 'created_at', 'updated_at', 
            'confirmed_at', 'completed_at', 'cancelled_at'
        ]

    def get_items_count(self, obj):
        return obj.items.count()


class OrderListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for order listings.
    """
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)
    customer_mobile = serializers.CharField(source='customer.mobile', read_only=True)
    items_count = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'customer_name', 'customer_mobile',
            'status', 'payment_status', 'total_amount', 'scheduled_date',
            'scheduled_time_slot', 'items_count', 'created_at'
        ]

    def get_items_count(self, obj):
        return obj.items.count()


class CreateOrderSerializer(serializers.Serializer):
    """
    Serializer for creating orders from cart.
    """
    cart_id = serializers.CharField()
    delivery_address = serializers.JSONField()
    payment_method = serializers.ChoiceField(
        choices=[('razorpay', 'Razorpay'), ('cod', 'Cash on Delivery')],
        default='razorpay'
    )
    scheduled_date = serializers.DateField(required=False)
    scheduled_time_slot = serializers.CharField(max_length=50, required=False)
    customer_notes = serializers.CharField(max_length=1000, required=False)

    def validate_delivery_address(self, value):
        """Validate delivery address structure"""
        required_fields = ['house_number', 'street_name', 'city', 'state', 'pincode']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f"Missing required field: {field}")
        return value


class OrderStatusUpdateSerializer(serializers.Serializer):
    """
    Serializer for updating order status.
    """
    status = serializers.ChoiceField(choices=Order.STATUS_CHOICES)
    reason = serializers.CharField(max_length=500, required=False)
    admin_notes = serializers.CharField(max_length=1000, required=False)


class OrderCancellationSerializer(serializers.ModelSerializer):
    """
    Serializer for order cancellation.
    """
    order_number = serializers.CharField(source='order.order_number', read_only=True)
    cancelled_by_name = serializers.CharField(source='cancelled_by.get_full_name', read_only=True)

    class Meta:
        model = OrderCancellation
        fields = [
            'id', 'order', 'order_number', 'reason', 'description',
            'cancelled_by', 'cancelled_by_name', 'refund_amount',
            'refund_processed', 'cancelled_at'
        ]
        read_only_fields = ['cancelled_at']


class OrderRescheduleSerializer(serializers.ModelSerializer):
    """
    Serializer for order rescheduling.
    """
    order_number = serializers.CharField(source='order.order_number', read_only=True)
    requested_by_name = serializers.CharField(source='requested_by.get_full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)

    class Meta:
        model = OrderReschedule
        fields = [
            'id', 'order', 'order_number', 'original_date', 'original_time_slot',
            'new_date', 'new_time_slot', 'reason', 'requested_by', 'requested_by_name',
            'approved', 'approved_by', 'approved_by_name', 'created_at'
        ]
        read_only_fields = ['created_at']


class OrderStatusHistorySerializer(serializers.ModelSerializer):
    """
    Serializer for order status history.
    """
    changed_by_name = serializers.CharField(source='changed_by.get_full_name', read_only=True)

    class Meta:
        model = OrderStatusHistory
        fields = [
            'id', 'previous_status', 'new_status', 'changed_by',
            'changed_by_name', 'reason', 'timestamp'
        ]


class AssignProviderSerializer(serializers.Serializer):
    """
    Serializer for assigning service provider to order.
    """
    provider_id = serializers.IntegerField()
    notes = serializers.CharField(max_length=500, required=False)

    def validate_provider_id(self, value):
        """Validate that provider exists and is active"""
        from authentication.models import User
        try:
            provider = User.objects.get(id=value, user_type='provider', is_active=True)
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("Invalid or inactive service provider.")


class OrderPaymentUpdateSerializer(serializers.Serializer):
    """
    Serializer for updating payment information.
    """
    payment_id = serializers.CharField(max_length=100)
    payment_signature = serializers.CharField(max_length=200, required=False)
    payment_status = serializers.ChoiceField(choices=Order.PAYMENT_STATUS_CHOICES)


class OrderSummarySerializer(serializers.ModelSerializer):
    """
    Minimal serializer for order summaries and dashboards.
    """
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)

    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'customer_name', 'status',
            'payment_status', 'total_amount', 'created_at'
        ]


class CustomerOrderSerializer(serializers.ModelSerializer):
    """
    Serializer for customer's own orders (limited fields).
    """
    items = OrderItemSerializer(many=True, read_only=True)
    items_count = serializers.SerializerMethodField()
    can_cancel = serializers.ReadOnlyField(source='can_be_cancelled')
    can_reschedule = serializers.ReadOnlyField(source='can_be_rescheduled')

    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'status', 'payment_status', 'payment_method',
            'subtotal', 'tax_amount', 'discount_amount', 'total_amount',
            'coupon_code', 'coupon_discount', 'delivery_address',
            'scheduled_date', 'scheduled_time_slot', 'customer_notes',
            'items', 'items_count', 'can_cancel', 'can_reschedule',
            'created_at', 'updated_at'
        ]

    def get_items_count(self, obj):
        return obj.items.count()


class ProviderOrderSerializer(serializers.ModelSerializer):
    """
    Serializer for service provider's assigned orders.
    """
    items = OrderItemSerializer(many=True, read_only=True)
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)
    customer_mobile = serializers.CharField(source='customer.mobile', read_only=True)

    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'customer_name', 'customer_mobile',
            'status', 'delivery_address', 'scheduled_date', 'scheduled_time_slot',
            'customer_notes', 'items', 'created_at'
        ]
