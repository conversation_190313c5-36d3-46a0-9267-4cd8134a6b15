#!/usr/bin/env python
"""
Simple migration script without database reset.
"""
import os
import django
from django.core.management import execute_from_command_line

def simple_migration():
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
    django.setup()
    
    print("🚀 Running migrations with improved database router...")
    
    # Step 1: Migrate core Django apps to default database
    print("\n📊 Step 1: Migrating core apps to default database...")
    core_apps = ['contenttypes', 'auth', 'admin', 'sessions', 'authentication']
    
    for app in core_apps:
        try:
            print(f"  🔧 Migrating {app} to default...")
            execute_from_command_line([
                'manage.py', 'migrate', app,
                '--database=default',
                '--skip-checks',
                '--verbosity=1'
            ])
            print(f"  ✅ {app} completed")
        except Exception as e:
            print(f"  ⚠️ {app} issue: {str(e)[:100]}...")
            continue
    
    # Step 2: Migrate custom apps to their specific databases
    print("\n📊 Step 2: Migrating custom apps to their databases...")
    
    custom_app_mapping = {
        'catalogue': 'catalogue_db',
        'cart': 'cart_db',
        'coupons': 'coupons_db',
        'orders': 'orders_db',
        'payments': 'payments_db',
        'providers': 'providers_db'
    }
    
    for app, db in custom_app_mapping.items():
        try:
            print(f"  🔧 Migrating {app} to {db}...")
            execute_from_command_line([
                'manage.py', 'migrate', app,
                f'--database={db}',
                '--skip-checks',
                '--verbosity=1'
            ])
            print(f"  ✅ {app} completed")
        except Exception as e:
            print(f"  ⚠️ {app} issue: {str(e)[:100]}...")
            continue
    
    print("\n🎉 Migration completed!")
    print("\n📋 Next steps:")
    print("1. Run: python verify_databases.py")
    print("2. Run: python manage.py createsuperuser")
    print("3. Run: python manage.py runserver")

if __name__ == '__main__':
    simple_migration()
