from django.db import models
from django.conf import settings
from decimal import Decimal
from django.core.exceptions import ValidationError
from catalogue.models import Service


class Cart(models.Model):
    """
    Shopping cart model supporting both authenticated and anonymous users.
    Stores cart state persistently for abandoned cart recovery.
    """
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='carts'
    )
    session_key = models.CharField(
        max_length=40,
        null=True,
        blank=True,
        unique=True,
        help_text="Session key for anonymous users"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    # Calculated totals
    sub_total = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00')
    )
    tax_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00')
    )
    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00')
    )
    minimum_order_fee_applied = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00')
    )
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00')
    )
    coupon_code_applied = models.CharField(
        max_length=50,
        blank=True,
        null=True
    )

    class Meta:
        constraints = [
            models.CheckConstraint(
                check=models.Q(user__isnull=False) | models.Q(session_key__isnull=False),
                name='cart_must_have_user_or_session'
            )
        ]
        ordering = ['-updated_at']

    def clean(self):
        if not self.user and not self.session_key:
            raise ValidationError("Cart must have either a user or session key.")

    def get_items_count(self):
        """Get total number of items in cart"""
        return self.items.aggregate(
            total=models.Sum('quantity')
        )['total'] or 0

    def get_unique_services_count(self):
        """Get count of unique services in cart"""
        return self.items.count()

    def calculate_subtotal(self):
        """Calculate subtotal from cart items"""
        subtotal = Decimal('0.00')
        for item in self.items.all():
            subtotal += item.get_total_price()
        return subtotal

    def update_totals(self):
        """Update all calculated totals"""
        self.sub_total = self.calculate_subtotal()
        # Tax and other calculations will be handled by external services
        self.save()

    def __str__(self):
        if self.user:
            return f"Cart {self.id} for {self.user.get_full_name() or self.user.mobile}"
        return f"Cart {self.id} for session {self.session_key}"


class CartItem(models.Model):
    """
    Individual items in a shopping cart.
    Stores price at time of addition for historical accuracy.
    """
    cart = models.ForeignKey(
        Cart,
        on_delete=models.CASCADE,
        related_name='items'
    )
    service = models.ForeignKey(
        Service,
        on_delete=models.CASCADE
    )
    quantity = models.PositiveIntegerField(default=1)
    price_at_add = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Price of service when added to cart"
    )
    discount_at_add = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Discount applied when added to cart"
    )
    added_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('cart', 'service')
        ordering = ['added_at']

    def get_total_price(self):
        """Calculate total price for this cart item"""
        return (self.price_at_add - self.discount_at_add) * self.quantity

    def get_savings(self):
        """Calculate savings from discount"""
        return self.discount_at_add * self.quantity

    def save(self, *args, **kwargs):
        # Update cart totals when cart item is saved
        super().save(*args, **kwargs)
        self.cart.update_totals()

    def delete(self, *args, **kwargs):
        cart = self.cart
        super().delete(*args, **kwargs)
        cart.update_totals()

    def __str__(self):
        return f"{self.quantity} x {self.service.title} in Cart {self.cart.id}"
