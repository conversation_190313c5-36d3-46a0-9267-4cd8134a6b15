#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to properly migrate each database with correct app isolation.
"""
import os
import sys
import django
from django.core.management import execute_from_command_line

def run_migrations():
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
    django.setup()

    # Database and app mapping
    db_app_mapping = {
        'default': ['admin', 'auth', 'contenttypes', 'sessions', 'authentication'],
        'catalogue_db': ['catalogue'],
        'cart_db': ['cart'],
        'coupons_db': ['coupons'],
        'orders_db': ['orders'],
        'payments_db': ['payments'],
        'providers_db': ['providers']
    }

    print("🚀 Starting database migrations...")

    for db_name, apps in db_app_mapping.items():
        print(f"\n📊 Migrating database: {db_name}")
        print(f"📱 Apps: {', '.join(apps)}")

        try:
            # Migrate each app individually to the correct database
            for app in apps:
                print(f"  ⚡ Migrating {app} to {db_name}...")
                execute_from_command_line([
                    'manage.py', 'migrate', app,
                    f'--database={db_name}',
                    '--verbosity=1'
                ])
                print(f"  ✅ {app} migrated successfully")

            print(f"✅ Database {db_name} migration completed!")

        except Exception as e:
            print(f"❌ Error migrating {db_name}: {e}")
            continue

    print("\n🎉 All migrations completed!")

if __name__ == '__main__':
    run_migrations()
