{% if result_hidden_fields %}
<div class="hiddenfields">{# DIV for HTML validation #}
{% for item in result_hidden_fields %}{{ item }}{% endfor %}
</div>
{% endif %}
{% if results %}
<div class="results">
<table cellspacing="0" id="result_list">
<thead>
<tr>
    {% for header in result_headers %}
        <th scope="col"{{ header.class_attrib }}>
            <div class="text"><span>{{ header.text|capfirst }}</span></div>
            <div class="clear"></div>
        </th>
    {% endfor %}
</tr>
</thead>
<tbody>
{% for result in results %}
{% if result.form.non_field_errors %}
    <tr><td colspan="{{ result|length }}">{{ result.form.non_field_errors }}</td></tr>
{% endif %}
<tr class="{% cycle 'row1' 'row2' %}">{% for item in result %}{{ item }}{% endfor %}</tr>
{% endfor %}
</tbody>
</table>
</div>
{% endif %}
