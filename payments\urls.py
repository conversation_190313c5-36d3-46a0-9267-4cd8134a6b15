from django.urls import path
from . import views

app_name = 'payments'

urlpatterns = [
    # Payment initiation
    path('initiate/', views.initiate_payment, name='initiate-payment'),
    path('razorpay/callback/', views.razorpay_callback, name='razorpay-callback'),
    path('cod/confirm/', views.cod_confirm, name='cod-confirm'),
    
    # Payment status
    path('status/<str:transaction_id>/', views.payment_status, name='payment-status'),
    path('verify/', views.verify_payment, name='verify-payment'),
    
    # Refunds
    path('refund/', views.initiate_refund, name='initiate-refund'),
    path('refund/status/<str:refund_id>/', views.refund_status, name='refund-status'),
    
    # Webhooks
    path('webhook/razorpay/', views.razorpay_webhook, name='razorpay-webhook'),
    
    # Admin views
    path('transactions/', views.TransactionListView.as_view(), name='transaction-list'),
    path('transactions/<uuid:pk>/', views.TransactionDetailView.as_view(), name='transaction-detail'),
]
