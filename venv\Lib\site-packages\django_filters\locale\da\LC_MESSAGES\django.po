msgid ""
msgstr ""
"Project-Id-Version: django-filter\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-10 11:07+0000\n"
"PO-Revision-Date: 2017-10-28\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON> <<EMAIL>>\n"
"Language: da\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.0.1\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: conf.py:16
msgid "date"
msgstr "dato"

#: conf.py:17
msgid "year"
msgstr "år"

#: conf.py:18
msgid "month"
msgstr "måned"

#: conf.py:19
msgid "day"
msgstr "dag"

#: conf.py:20
msgid "week day"
msgstr "ugedag"

#: conf.py:21
msgid "hour"
msgstr "time"

#: conf.py:22
msgid "minute"
msgstr "minut"

#: conf.py:23
msgid "second"
msgstr "sekund"

#: conf.py:27 conf.py:28
msgid "contains"
msgstr "indeholder"

#: conf.py:29
msgid "is in"
msgstr "er i"

#: conf.py:30
msgid "is greater than"
msgstr "er større end"

#: conf.py:31
msgid "is greater than or equal to"
msgstr "er større end eller lig med"

#: conf.py:32
msgid "is less than"
msgstr "er mindre end"

#: conf.py:33
msgid "is less than or equal to"
msgstr "er mindre end eller lig med"

#: conf.py:34 conf.py:35
msgid "starts with"
msgstr "starter med"

#: conf.py:36 conf.py:37
msgid "ends with"
msgstr "slutter med"

#: conf.py:38
msgid "is in range"
msgstr "er i intervallet"

#: conf.py:39
msgid "is null"
msgstr ""

#: conf.py:40 conf.py:41
msgid "matches regex"
msgstr "matcher regex"

#: conf.py:42 conf.py:49
msgid "search"
msgstr "søg"

#: conf.py:44
msgid "is contained by"
msgstr "er indeholdt af"

#: conf.py:45
msgid "overlaps"
msgstr "overlapper"

#: conf.py:46
msgid "has key"
msgstr "har string"

#: conf.py:47
msgid "has keys"
msgstr "har stringe"

#: conf.py:48
msgid "has any keys"
msgstr "har hvilken som helst string"

#: fields.py:94
msgid "Select a lookup."
msgstr ""

#: fields.py:198
msgid "Range query expects two values."
msgstr "Interval forespørgslen forventer to værdier."

#: filters.py:437
msgid "Today"
msgstr "I dag"

#: filters.py:438
msgid "Yesterday"
msgstr "I går"

#: filters.py:439
msgid "Past 7 days"
msgstr "Sidste 7 dage"

#: filters.py:440
msgid "This month"
msgstr "Denne måned"

#: filters.py:441
msgid "This year"
msgstr "Dette år"

#: filters.py:543
msgid "Multiple values may be separated by commas."
msgstr "Flere værdier kan adskilles via komma."

#: filters.py:721
#, python-format
msgid "%s (descending)"
msgstr "%s (aftagende)"

#: filters.py:737
msgid "Ordering"
msgstr "Sortering"

#: rest_framework/filterset.py:33
#: templates/django_filters/rest_framework/form.html:5
#, fuzzy
msgid "Submit"
msgstr "Indsend"

#: templates/django_filters/rest_framework/crispy_form.html:4
#: templates/django_filters/rest_framework/form.html:2
#, fuzzy
msgid "Field filters"
msgstr "Felt filtre"

#: utils.py:308
msgid "exclude"
msgstr "udelad"

#: widgets.py:58
msgid "All"
msgstr "Alle"

#: widgets.py:162
msgid "Unknown"
msgstr "Ukendt"

#: widgets.py:162
msgid "Yes"
msgstr "Ja"

#: widgets.py:162
msgid "No"
msgstr "Nej"

#~ msgid "Any date"
#~ msgstr "Hvilken som helst dag"
