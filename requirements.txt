# Core Django
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1

# Database - Use latest version to avoid build issues
psycopg2-binary>=2.9.0

# Authentication & JWT
djangorestframework-simplejwt==5.3.0

# Environment variables
python-decouple==3.8

# Redis for caching and sessions
redis==5.0.1
django-redis==5.4.0

# Rate limiting
django-ratelimit==4.1.0

# SMS Gateway (MSG91)
requests==2.31.0

# Image handling
Pillow>=10.0.0

# Validation
phonenumbers==8.13.25

# Timezone support
pytz==2023.3

# Development tools
django-extensions==3.2.3

# API documentation
drf-spectacular==0.26.5

# MPTT for hierarchical data (categories)
django-mptt==0.14.0

# Database URL parsing for multiple databases
dj-database-url==2.1.0

# Additional utilities
whitenoise==6.6.0

# Filtering for DRF
django-filter==23.3

# Recursive serializers for nested data
djangorestframework-recursive==0.1.2
