from rest_framework import serializers
from django.utils import timezone
from .models import Coupon, UsedCoupon, CouponApplication


class CouponSerializer(serializers.ModelSerializer):
    """
    Serializer for Coupon model with validation and calculated fields.
    """
    is_valid = serializers.ReadOnlyField(source='is_valid')
    target_categories_names = serializers.StringRelatedField(
        source='target_categories', 
        many=True, 
        read_only=True
    )
    target_services_names = serializers.StringRelatedField(
        source='target_services', 
        many=True, 
        read_only=True
    )
    total_usage = serializers.SerializerMethodField()

    class Meta:
        model = Coupon
        fields = [
            'id', 'code', 'description', 'discount_type', 'value',
            'min_cart_value', 'max_discount_value', 'valid_from', 'valid_to',
            'usage_limit_per_coupon', 'usage_limit_per_user', 'is_active',
            'applies_to', 'target_categories', 'target_categories_names',
            'target_services', 'target_services_names', 'is_valid',
            'total_usage', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_total_usage(self, obj):
        """Get total number of times this coupon has been used"""
        return obj.used_coupons.count()

    def validate_code(self, value):
        """Ensure coupon code is uppercase and unique"""
        return value.upper().strip()

    def validate(self, data):
        """Validate coupon configuration"""
        # Validate percentage discount
        if data.get('discount_type') == 'percentage':
            if data.get('value', 0) > 100:
                raise serializers.ValidationError(
                    "Percentage discount cannot exceed 100%"
                )
        
        # Validate date range
        valid_from = data.get('valid_from')
        valid_to = data.get('valid_to')
        if valid_from and valid_to and valid_from >= valid_to:
            raise serializers.ValidationError(
                "Valid from date must be before valid to date"
            )
        
        return data


class CouponListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for coupon listings.
    """
    is_valid = serializers.ReadOnlyField(source='is_valid')
    total_usage = serializers.SerializerMethodField()

    class Meta:
        model = Coupon
        fields = [
            'id', 'code', 'description', 'discount_type', 'value',
            'min_cart_value', 'valid_from', 'valid_to', 'is_active',
            'applies_to', 'is_valid', 'total_usage'
        ]

    def get_total_usage(self, obj):
        return obj.used_coupons.count()


class CouponCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating coupons.
    """
    class Meta:
        model = Coupon
        fields = [
            'code', 'description', 'discount_type', 'value',
            'min_cart_value', 'max_discount_value', 'valid_from', 'valid_to',
            'usage_limit_per_coupon', 'usage_limit_per_user', 'is_active',
            'applies_to', 'target_categories', 'target_services'
        ]

    def validate_code(self, value):
        return value.upper().strip()


class ApplyCouponSerializer(serializers.Serializer):
    """
    Serializer for applying coupon to cart.
    """
    coupon_code = serializers.CharField(max_length=50)
    cart_total = serializers.DecimalField(max_digits=10, decimal_places=2)

    def validate_coupon_code(self, value):
        """Validate and normalize coupon code"""
        code = value.upper().strip()
        try:
            coupon = Coupon.objects.get(code=code)
            if not coupon.is_valid():
                raise serializers.ValidationError("Coupon is not valid or has expired")
            return code
        except Coupon.DoesNotExist:
            raise serializers.ValidationError("Invalid coupon code")


class CouponValidationSerializer(serializers.Serializer):
    """
    Serializer for validating coupon without applying.
    """
    coupon_code = serializers.CharField(max_length=50)
    cart_total = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    user_id = serializers.IntegerField(required=False)

    def validate_coupon_code(self, value):
        return value.upper().strip()


class UsedCouponSerializer(serializers.ModelSerializer):
    """
    Serializer for tracking coupon usage.
    """
    coupon_code = serializers.CharField(source='coupon.code', read_only=True)
    user_info = serializers.SerializerMethodField()

    class Meta:
        model = UsedCoupon
        fields = [
            'id', 'user', 'user_info', 'coupon', 'coupon_code',
            'cart_total', 'discount_amount', 'used_at', 'session_key'
        ]
        read_only_fields = ['used_at']

    def get_user_info(self, obj):
        if obj.user:
            return obj.user.get_full_name() or obj.user.mobile
        return f"Anonymous (Session: {obj.session_key})"


class CouponApplicationSerializer(serializers.ModelSerializer):
    """
    Serializer for sequential coupon applications.
    """
    coupon_code = serializers.CharField(source='coupon.code', read_only=True)

    class Meta:
        model = CouponApplication
        fields = [
            'id', 'cart_id', 'coupon', 'coupon_code', 'order_applied',
            'cart_amount_before', 'discount_amount', 'cart_amount_after',
            'applied_at'
        ]
        read_only_fields = ['applied_at']


class CouponDiscountCalculationSerializer(serializers.Serializer):
    """
    Serializer for calculating discount amounts.
    """
    coupon_code = serializers.CharField()
    cart_total = serializers.DecimalField(max_digits=10, decimal_places=2)
    discount_amount = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    final_amount = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    is_applicable = serializers.BooleanField(read_only=True)
    message = serializers.CharField(read_only=True)


class SequentialCouponApplicationSerializer(serializers.Serializer):
    """
    Serializer for applying multiple coupons sequentially.
    """
    cart_id = serializers.CharField()
    coupon_codes = serializers.ListField(
        child=serializers.CharField(max_length=50),
        min_length=1
    )
    initial_cart_total = serializers.DecimalField(max_digits=10, decimal_places=2)
    
    # Response fields
    applications = CouponApplicationSerializer(many=True, read_only=True)
    final_cart_total = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    total_discount = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    
    def validate_coupon_codes(self, value):
        """Validate all coupon codes exist and are valid"""
        codes = [code.upper().strip() for code in value]
        
        # Check for duplicates
        if len(codes) != len(set(codes)):
            raise serializers.ValidationError("Duplicate coupon codes are not allowed")
        
        # Validate each coupon exists
        for code in codes:
            try:
                coupon = Coupon.objects.get(code=code)
                if not coupon.is_valid():
                    raise serializers.ValidationError(f"Coupon '{code}' is not valid or has expired")
            except Coupon.DoesNotExist:
                raise serializers.ValidationError(f"Invalid coupon code: '{code}'")
        
        return codes
