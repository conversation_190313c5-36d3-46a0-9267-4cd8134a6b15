# 🚀 Production Deployment Guide

## 📋 Pre-Deployment Checklist

### 1. **Environment Configuration**
- [ ] Copy `.env.production` to `.env` and update all values
- [ ] Set `DEBUG=False`
- [ ] Configure strong `SECRET_KEY` (use <PERSON><PERSON><PERSON>'s get_random_secret_key())
- [ ] Set proper `ALLOWED_HOSTS`
- [ ] Configure production database credentials
- [ ] Set up Redis server and configure `REDIS_URL`
- [ ] Enable rate limiting: `RATELIMIT_ENABLE=True`
- [ ] Configure HTTPS security settings
- [ ] Set up MSG91 credentials for OTP
- [ ] Configure production email settings

### 2. **Security Configuration**
- [ ] Enable HTTPS redirect: `SECURE_SSL_REDIRECT=True`
- [ ] Configure HSTS: `SECURE_HSTS_SECONDS=31536000`
- [ ] Set secure cookies: `SESSION_COOKIE_SECURE=True`, `CSRF_COOKIE_SECURE=True`
- [ ] Configure CSRF trusted origins
- [ ] Set proper CORS allowed origins
- [ ] Review and update `ALLOWED_HOSTS`

### 3. **Database Setup**
- [ ] Create production PostgreSQL database
- [ ] Configure database user with proper permissions
- [ ] Run migrations: `python manage.py migrate`
- [ ] Create superuser: `python manage.py createsuperuser`
- [ ] Load initial data if needed

### 4. **Redis Setup**
- [ ] Install and configure Redis server
- [ ] Test Redis connection
- [ ] Configure Redis for caching and sessions

### 5. **Static Files**
- [ ] Configure static files collection
- [ ] Set up web server (Nginx) for static file serving
- [ ] Run: `python manage.py collectstatic`

### 6. **Application Server**
- [ ] Install and configure Gunicorn or uWSGI
- [ ] Create systemd service file
- [ ] Configure process management

### 7. **Web Server**
- [ ] Configure Nginx or Apache
- [ ] Set up SSL certificates (Let's Encrypt recommended)
- [ ] Configure reverse proxy
- [ ] Set up rate limiting at web server level

### 8. **Monitoring & Logging**
- [ ] Configure log rotation
- [ ] Set up monitoring (health checks)
- [ ] Configure error tracking (Sentry recommended)
- [ ] Set up backup procedures

## 🔧 Quick Production Setup Commands

### Generate Secret Key
```python
from django.core.management.utils import get_random_secret_key
print(get_random_secret_key())
```

### Database Setup
```bash
# Create database
sudo -u postgres createdb auth_service_prod
sudo -u postgres createuser --interactive your_db_user

# Run migrations
python manage.py migrate
python manage.py createsuperuser
```

### Install Production Dependencies
```bash
pip install gunicorn
pip install redis
```

### Collect Static Files
```bash
python manage.py collectstatic --noinput
```

### Test Production Settings
```bash
python manage.py check --deploy
```

## 🚨 Security Warnings

1. **Never commit `.env` file to version control**
2. **Use strong, unique passwords for all services**
3. **Regularly update dependencies**
4. **Monitor failed login attempts**
5. **Set up proper backup procedures**
6. **Use HTTPS in production**
7. **Configure proper firewall rules**

## 📊 Production Monitoring

### Health Check Endpoint
The application includes health check endpoints for monitoring:
- `/api/auth/health/` - Basic health check
- `/admin/` - Admin interface availability

### Key Metrics to Monitor
- Response times
- Error rates
- Database connections
- Redis connections
- Failed login attempts
- OTP delivery rates

## 🔄 Deployment Process

1. **Code Deployment**
   ```bash
   git pull origin main
   pip install -r requirements.txt
   python manage.py migrate
   python manage.py collectstatic --noinput
   sudo systemctl restart your-app-service
   ```

2. **Zero-Downtime Deployment**
   - Use blue-green deployment
   - Or rolling updates with load balancer

## 🆘 Troubleshooting

### Common Issues
1. **Redis Connection Error**: Check Redis service status
2. **Database Connection Error**: Verify credentials and permissions
3. **Static Files Not Loading**: Run collectstatic and check Nginx config
4. **CORS Errors**: Update CORS_ALLOWED_ORIGINS
5. **Rate Limiting Issues**: Check Redis cache backend

### Debug Commands
```bash
# Check deployment issues
python manage.py check --deploy

# Test database connection
python manage.py dbshell

# Check Redis connection
python manage.py shell
>>> from django.core.cache import cache
>>> cache.set('test', 'value')
>>> cache.get('test')
```

## 📞 Support

For production issues:
1. Check application logs: `/path/to/logs/django.log`
2. Check web server logs
3. Monitor system resources
4. Review security logs

**Remember**: Always test in a staging environment before production deployment!
