# Core Django dependencies - assuming requirements.txt covers this
-r requirements.txt

# Production WSGI server
gunicorn==21.2.0

# Database adapter for PostgreSQL on Render
psycopg2-binary==2.9.9 # Crucial for PostgreSQL connection

# For parsing DATABASE_URL provided by Render
dj-database-url==1.3.0 # Highly recommended for Render DB connection

# Redis & Caching
django-redis==5.4.0 # Essential for Redis caching and sessions
redis==5.0.1 # The underlying Redis client library

# Rate Limiting
django-ratelimit==4.0.0 # From your previous AI summary

# Production monitoring and logging
sentry-sdk[django]==1.38.0

# Production security
django-security==0.17.0

# Process management (Note: Supervisor is generally not used on Render Web Services)
# supervisor==4.2.5

# Health checks
django-health-check==3.17.0

# Performance monitoring (Note: Django-Silk typically not enabled in production)
# django-silk==5.0.4

# Database connection pooling (often handled by psycopg2-binary or Render's internal pooling)
# django-db-pool==1.0.0

# Production utilities
whitenoise==6.6.0  # Static file serving
django-storages==1.14.2  # Cloud storage support (optional)
boto3==1.34.0  # AWS S3 support (optional)

# Backup utilities (Note: Render's database backups are usually preferred)
# django-dbbackup==4.0.2
