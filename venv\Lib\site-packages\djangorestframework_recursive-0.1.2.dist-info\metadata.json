{"license": "BSD", "name": "djangorestframework-recursive", "metadata_version": "2.0", "generator": "bdist_wheel (0.24.0)", "summary": "Recursive Serialization for Django REST framework", "run_requires": [{"requires": ["Django", "djangorestframework (>=3.0)"]}], "version": "0.1.2", "extensions": {"python.details": {"project_urls": {"Home": "https://github.com/heywbj/django-rest-framework-recursive"}, "document_names": {"description": "DESCRIPTION.rst"}, "contacts": [{"role": "author", "email": "<EMAIL>", "name": "<PERSON>"}]}}, "classifiers": ["Development Status :: 2 - Pre-Alpha", "Environment :: Web Environment", "Framework :: Django", "Intended Audience :: <PERSON><PERSON><PERSON>", "License :: OSI Approved :: BSD License", "Operating System :: OS Independent", "Natural Language :: English", "Programming Language :: Python :: 2", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.3", "Programming Language :: Python :: 3.4", "Topic :: Internet :: WWW/HTTP"], "extras": []}