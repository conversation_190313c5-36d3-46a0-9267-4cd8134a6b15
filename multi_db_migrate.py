#!/usr/bin/env python
"""
Proper multi-database migration script for microservices.
"""
import os
import django
from django.core.management import execute_from_command_line

def multi_db_migrate():
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
    django.setup()
    
    print("🚀 Multi-Database Migration for Microservices...")
    
    # Step 1: Migrate core Django apps to all databases first
    databases = ['default', 'catalogue_db', 'cart_db', 'coupons_db', 'orders_db', 'payments_db', 'providers_db']
    core_apps = ['contenttypes', 'auth', 'admin', 'sessions']
    
    print("\n📊 Step 1: Migrating core Django apps to all databases...")
    for db in databases:
        print(f"\n🗄️ Migrating core apps to {db}...")
        for app in core_apps:
            try:
                print(f"  Migrating {app} to {db}...")
                execute_from_command_line([
                    'manage.py', 'migrate', app, 
                    f'--database={db}', 
                    '--skip-checks',
                    '--verbosity=1'
                ])
                print(f"  ✅ {app} migrated to {db}")
            except Exception as e:
                print(f"  ⚠️ {app} to {db}: {e}")
                continue
    
    # Step 2: Migrate specific apps to their designated databases
    print("\n📊 Step 2: Migrating microservice apps to their databases...")
    
    app_db_mapping = {
        'authentication': 'default',
        'catalogue': 'catalogue_db',
        'cart': 'cart_db',
        'coupons': 'coupons_db',
        'orders': 'orders_db',
        'payments': 'payments_db',
        'providers': 'providers_db'
    }
    
    for app, db in app_db_mapping.items():
        print(f"\n🔧 Migrating {app} to {db}...")
        try:
            execute_from_command_line([
                'manage.py', 'migrate', app, 
                f'--database={db}', 
                '--skip-checks',
                '--verbosity=1'
            ])
            print(f"✅ {app} migrated successfully to {db}")
        except Exception as e:
            print(f"❌ {app} migration error: {e}")
            continue
    
    print("\n🎉 Multi-database migration completed!")
    print("\n📋 Database Summary:")
    print("  🗄️ home_services_auth: authentication + core Django apps")
    print("  🗄️ home_services_catalogue: catalogue + core Django apps")
    print("  🗄️ home_services_cart: cart + core Django apps")
    print("  🗄️ home_services_coupons: coupons + core Django apps")
    print("  🗄️ home_services_orders: orders + core Django apps")
    print("  🗄️ home_services_payments: payments + core Django apps")
    print("  🗄️ home_services_providers: providers + core Django apps")
    
    print("\n📋 Next steps:")
    print("1. Run: python manage.py createsuperuser")
    print("2. Run: python manage.py runserver")
    print("3. Test APIs at: http://127.0.0.1:8000/api/")

if __name__ == '__main__':
    multi_db_migrate()
