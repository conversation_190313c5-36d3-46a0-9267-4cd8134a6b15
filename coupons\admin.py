from django.contrib import admin
from django.utils.html import format_html
from .models import Coupon, UsedCoupon, CouponApplication


@admin.register(Coupon)
class CouponAdmin(admin.ModelAdmin):
    """
    Admin interface for Coupon model.
    """
    list_display = [
        'code', 'description', 'discount_type', 'value', 
        'min_cart_value', 'applies_to', 'is_active', 
        'is_valid_status', 'total_usage', 'valid_from', 'valid_to'
    ]
    list_filter = [
        'discount_type', 'applies_to', 'is_active', 
        'valid_from', 'valid_to', 'created_at'
    ]
    search_fields = ['code', 'description']
    readonly_fields = ['created_at', 'updated_at', 'is_valid_status', 'total_usage']
    filter_horizontal = ['target_categories', 'target_services']
    
    fieldsets = (
        (None, {
            'fields': ('code', 'description', 'is_active')
        }),
        ('Discount Configuration', {
            'fields': (
                'discount_type', 'value', 'min_cart_value', 'max_discount_value'
            )
        }),
        ('Validity Period', {
            'fields': ('valid_from', 'valid_to', 'is_valid_status')
        }),
        ('Usage Limits', {
            'fields': ('usage_limit_per_coupon', 'usage_limit_per_user', 'total_usage')
        }),
        ('Application Rules', {
            'fields': ('applies_to', 'target_categories', 'target_services'),
            'description': 'Define where this coupon can be applied'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def is_valid_status(self, obj):
        """Display coupon validity status with color coding"""
        if obj.is_valid():
            return format_html(
                '<span style="color: green; font-weight: bold;">✓ Valid</span>'
            )
        else:
            return format_html(
                '<span style="color: red; font-weight: bold;">✗ Invalid</span>'
            )
    is_valid_status.short_description = 'Status'
    
    def total_usage(self, obj):
        """Display total usage count"""
        count = obj.used_coupons.count()
        limit = obj.usage_limit_per_coupon
        if limit:
            return f"{count}/{limit}"
        return str(count)
    total_usage.short_description = 'Usage Count'
    
    def save_model(self, request, obj, form, change):
        """Ensure coupon code is uppercase"""
        obj.code = obj.code.upper()
        super().save_model(request, obj, form, change)


class CouponApplicationInline(admin.TabularInline):
    """
    Inline admin for coupon applications.
    """
    model = CouponApplication
    extra = 0
    readonly_fields = ['applied_at']
    fields = [
        'coupon', 'order_applied', 'cart_amount_before', 
        'discount_amount', 'cart_amount_after', 'applied_at'
    ]


@admin.register(UsedCoupon)
class UsedCouponAdmin(admin.ModelAdmin):
    """
    Admin interface for tracking coupon usage.
    """
    list_display = [
        'coupon_code', 'get_user_info', 'cart_total', 
        'discount_amount', 'savings_percentage', 'used_at'
    ]
    list_filter = ['used_at', 'coupon__discount_type']
    search_fields = [
        'coupon__code', 'user__mobile', 'user__email', 'session_key'
    ]
    readonly_fields = ['used_at', 'savings_percentage']
    
    fieldsets = (
        (None, {
            'fields': ('user', 'session_key', 'coupon')
        }),
        ('Transaction Details', {
            'fields': ('cart_total', 'discount_amount', 'savings_percentage')
        }),
        ('Timestamp', {
            'fields': ('used_at',)
        }),
    )
    
    def coupon_code(self, obj):
        return obj.coupon.code
    coupon_code.short_description = 'Coupon Code'
    
    def get_user_info(self, obj):
        if obj.user:
            return f"{obj.user.get_full_name() or obj.user.mobile} (User)"
        return f"Anonymous (Session: {obj.session_key})"
    get_user_info.short_description = 'User/Session'
    
    def savings_percentage(self, obj):
        if obj.cart_total > 0:
            percentage = (obj.discount_amount / obj.cart_total) * 100
            return f"{percentage:.1f}%"
        return "0%"
    savings_percentage.short_description = 'Savings %'


@admin.register(CouponApplication)
class CouponApplicationAdmin(admin.ModelAdmin):
    """
    Admin interface for sequential coupon applications.
    """
    list_display = [
        'cart_id', 'coupon_code', 'order_applied', 
        'cart_amount_before', 'discount_amount', 'cart_amount_after', 'applied_at'
    ]
    list_filter = ['applied_at', 'order_applied']
    search_fields = ['cart_id', 'coupon__code']
    readonly_fields = ['applied_at']
    
    fieldsets = (
        (None, {
            'fields': ('cart_id', 'coupon', 'order_applied')
        }),
        ('Amount Calculation', {
            'fields': ('cart_amount_before', 'discount_amount', 'cart_amount_after')
        }),
        ('Timestamp', {
            'fields': ('applied_at',)
        }),
    )
    
    def coupon_code(self, obj):
        return obj.coupon.code
    coupon_code.short_description = 'Coupon Code'


# Customize admin site headers
admin.site.site_header = "Home Services - Coupon Management"
admin.site.site_title = "Coupon Admin"
admin.site.index_title = "Coupon & Discount Management"
