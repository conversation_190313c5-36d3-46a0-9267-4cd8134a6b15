from django.urls import path
from . import views

app_name = 'orders'

urlpatterns = [
    # Order CRUD
    path('', views.OrderListCreateView.as_view(), name='order-list-create'),
    path('<str:order_number>/', views.OrderDetailView.as_view(), name='order-detail'),
    
    # Order management
    path('<str:order_number>/status/', views.update_order_status, name='update-order-status'),
    path('<str:order_number>/cancel/', views.cancel_order, name='cancel-order'),
    path('<str:order_number>/reschedule/', views.reschedule_order, name='reschedule-order'),
    path('<str:order_number>/assign-provider/', views.assign_provider, name='assign-provider'),
    path('<str:order_number>/payment/', views.update_payment_status, name='update-payment-status'),
    
    # Order tracking and history
    path('<str:order_number>/history/', views.OrderStatusHistoryView.as_view(), name='order-history'),
    path('<str:order_number>/reschedules/', views.OrderRescheduleListView.as_view(), name='order-reschedules'),
    
    # Dashboard
    path('dashboard/', views.order_dashboard, name='order-dashboard'),
]
