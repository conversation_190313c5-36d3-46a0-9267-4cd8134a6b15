from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsA<PERSON>enticated, IsAdminUser
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.utils import timezone
from .models import (
    ProviderProfile, ProviderDocument, ProviderBankDetails,
    ProviderPayoutRequest, ProviderAvailability
)
from .serializers import (
    ProviderProfileSerializer, ProviderProfileUpdateSerializer,
    ProviderDocumentSerializer, DocumentUploadSerializer,
    ProviderBankDetailsSerializer, ProviderPayoutRequestSerializer,
    PayoutRequestSerializer, ProviderAvailabilitySerializer,
    UpdateAvailabilitySerializer, ProviderVerificationSerializer,
    DocumentVerificationSerializer, ProcessPayoutSerializer,
    ProviderListSerializer
)


class ProviderProfileView(generics.RetrieveAPIView):
    """
    Get provider profile.
    """
    serializer_class = ProviderProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        # Get or create provider profile for the authenticated user
        profile, created = ProviderProfile.objects.get_or_create(
            user=self.request.user,
            defaults={
                'business_name': '',
                'verification_status': 'pending'
            }
        )
        return profile


class UpdateProviderProfileView(generics.UpdateAPIView):
    """
    Update provider profile.
    """
    serializer_class = ProviderProfileUpdateSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        profile, created = ProviderProfile.objects.get_or_create(
            user=self.request.user,
            defaults={
                'business_name': '',
                'verification_status': 'pending'
            }
        )
        return profile


class ProviderDocumentListView(generics.ListAPIView):
    """
    List provider documents.
    """
    serializer_class = ProviderDocumentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        profile = get_object_or_404(ProviderProfile, user=self.request.user)
        return ProviderDocument.objects.filter(provider=profile).order_by('-uploaded_at')


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def upload_document(request):
    """
    Upload provider document.
    """
    serializer = DocumentUploadSerializer(data=request.data)

    if serializer.is_valid():
        profile = get_object_or_404(ProviderProfile, user=request.user)

        # Check if document type already exists
        document_type = serializer.validated_data['document_type']
        existing_doc = ProviderDocument.objects.filter(
            provider=profile,
            document_type=document_type
        ).first()

        if existing_doc:
            # Update existing document
            for field, value in serializer.validated_data.items():
                setattr(existing_doc, field, value)
            existing_doc.verification_status = 'pending'
            existing_doc.save()
            document = existing_doc
        else:
            # Create new document
            document = ProviderDocument.objects.create(
                provider=profile,
                **serializer.validated_data,
                verification_status='pending'
            )

        return Response({
            'success': True,
            'message': 'Document uploaded successfully',
            'document': ProviderDocumentSerializer(document).data
        }, status=status.HTTP_201_CREATED)

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


class ProviderDocumentDetailView(generics.RetrieveAPIView):
    """
    Get document details.
    """
    permission_classes = [IsAuthenticated]
    
    def retrieve(self, request, *args, **kwargs):
        return Response({
            'message': 'Provider document detail view - To be implemented'
        })


class ProviderBankDetailsView(generics.RetrieveAPIView):
    """
    Get provider bank details.
    """
    permission_classes = [IsAuthenticated]
    
    def retrieve(self, request, *args, **kwargs):
        return Response({
            'message': 'Provider bank details view - To be implemented'
        })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_bank_details(request):
    """
    Update provider bank details.
    """
    return Response({
        'success': True,
        'message': 'Update bank details endpoint - To be implemented'
    })


class ProviderAvailabilityListView(generics.ListAPIView):
    """
    List provider availability.
    """
    permission_classes = [IsAuthenticated]
    
    def list(self, request, *args, **kwargs):
        return Response({
            'message': 'Provider availability list view - To be implemented'
        })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_availability(request):
    """
    Update provider availability.
    """
    return Response({
        'success': True,
        'message': 'Update availability endpoint - To be implemented'
    })


class ProviderPayoutListView(generics.ListAPIView):
    """
    List provider payouts.
    """
    permission_classes = [IsAuthenticated]
    
    def list(self, request, *args, **kwargs):
        return Response({
            'message': 'Provider payout list view - To be implemented'
        })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def request_payout(request):
    """
    Request payout.
    """
    return Response({
        'success': True,
        'message': 'Request payout endpoint - To be implemented'
    })


class ProviderPayoutDetailView(generics.RetrieveAPIView):
    """
    Get payout details.
    """
    permission_classes = [IsAuthenticated]
    
    def retrieve(self, request, *args, **kwargs):
        return Response({
            'message': 'Provider payout detail view - To be implemented'
        })


# Admin views
class AdminProviderListView(generics.ListAPIView):
    """
    Admin view to list all providers.
    """
    permission_classes = [IsAdminUser]
    
    def list(self, request, *args, **kwargs):
        return Response({
            'message': 'Admin provider list view - To be implemented'
        })


@api_view(['POST'])
@permission_classes([IsAdminUser])
def verify_provider(request, pk):
    """
    Verify provider profile.
    """
    return Response({
        'success': True,
        'message': 'Verify provider endpoint - To be implemented'
    })


class AdminProviderDocumentsView(generics.ListAPIView):
    """
    Admin view to list provider documents.
    """
    permission_classes = [IsAdminUser]
    
    def list(self, request, *args, **kwargs):
        return Response({
            'message': 'Admin provider documents view - To be implemented'
        })


@api_view(['POST'])
@permission_classes([IsAdminUser])
def verify_document(request, pk):
    """
    Verify provider document.
    """
    return Response({
        'success': True,
        'message': 'Verify document endpoint - To be implemented'
    })


class AdminPayoutListView(generics.ListAPIView):
    """
    Admin view to list all payouts.
    """
    permission_classes = [IsAdminUser]
    
    def list(self, request, *args, **kwargs):
        return Response({
            'message': 'Admin payout list view - To be implemented'
        })


@api_view(['POST'])
@permission_classes([IsAdminUser])
def process_payout(request, pk):
    """
    Process payout request.
    """
    return Response({
        'success': True,
        'message': 'Process payout endpoint - To be implemented'
    })
