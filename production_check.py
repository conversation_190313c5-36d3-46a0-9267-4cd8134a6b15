#!/usr/bin/env python
"""
Production Readiness Check Script
Run this before deploying to production to ensure everything is configured correctly.
"""

import os
import sys
import django
from pathlib import Path

# Setup Django
sys.path.append(str(Path(__file__).parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
django.setup()

from django.conf import settings
from django.core.management import call_command
from django.core.cache import cache
from django.db import connection
from decouple import config
import requests

class ProductionChecker:
    def __init__(self):
        self.checks_passed = 0
        self.checks_failed = 0
        self.warnings = 0

    def check(self, description, condition, is_warning=False):
        """Run a check and print result"""
        if condition:
            print(f"✅ {description}")
            self.checks_passed += 1
        else:
            if is_warning:
                print(f"⚠️  {description}")
                self.warnings += 1
            else:
                print(f"❌ {description}")
                self.checks_failed += 1

    def run_all_checks(self):
        print("🔍 Running Production Readiness Checks...\n")
        
        # Django Settings Checks
        print("📋 Django Configuration:")
        self.check("DEBUG is False", not settings.DEBUG)
        self.check("SECRET_KEY is not default", settings.SECRET_KEY != 'django-insecure-change-me-in-production')
        self.check("ALLOWED_HOSTS is configured", len(settings.ALLOWED_HOSTS) > 0 and 'localhost' not in settings.ALLOWED_HOSTS)
        
        # Database Checks
        print("\n💾 Database Configuration:")
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            self.check("Database connection works", True)
        except Exception as e:
            self.check(f"Database connection failed: {e}", False)
        
        self.check("Using PostgreSQL", 'postgresql' in settings.DATABASES['default']['ENGINE'])
        
        # Cache/Redis Checks
        print("\n🗄️  Cache Configuration:")
        self.check("Redis is enabled", getattr(settings, 'USE_REDIS', False))
        
        try:
            cache.set('test_key', 'test_value', timeout=10)
            cache_works = cache.get('test_key') == 'test_value'
            cache.delete('test_key')
            self.check("Cache backend works", cache_works)
        except Exception as e:
            self.check(f"Cache backend failed: {e}", False)
        
        # Security Checks
        print("\n🔒 Security Configuration:")
        self.check("Rate limiting is enabled", getattr(settings, 'RATELIMIT_ENABLE', False))
        self.check("HTTPS redirect enabled", getattr(settings, 'SECURE_SSL_REDIRECT', False))
        self.check("HSTS configured", getattr(settings, 'SECURE_HSTS_SECONDS', 0) > 0)
        self.check("Secure cookies enabled", getattr(settings, 'SESSION_COOKIE_SECURE', False))
        self.check("CSRF protection enabled", getattr(settings, 'CSRF_COOKIE_SECURE', False))
        
        # Environment Variables
        print("\n🌍 Environment Variables:")
        required_env_vars = [
            'SECRET_KEY', 'DB_PASSWORD', 'MSG91_AUTH_KEY', 'MSG91_TEMPLATE_ID'
        ]
        
        for var in required_env_vars:
            value = config(var, default='')
            self.check(f"{var} is set", bool(value))
        
        # MSG91 Configuration
        print("\n📱 SMS Configuration:")
        msg91_key = config('MSG91_AUTH_KEY', default='')
        msg91_template = config('MSG91_TEMPLATE_ID', default='')
        self.check("MSG91 auth key configured", bool(msg91_key))
        self.check("MSG91 template ID configured", bool(msg91_template))
        
        # Static Files
        print("\n📁 Static Files:")
        static_root = getattr(settings, 'STATIC_ROOT', None)
        self.check("STATIC_ROOT is configured", static_root is not None)
        if static_root:
            self.check("Static files directory exists", Path(static_root).exists(), is_warning=True)
        
        # Logging
        print("\n📝 Logging Configuration:")
        self.check("Logging is configured", bool(getattr(settings, 'LOGGING', {})))
        log_dir = Path(settings.BASE_DIR) / 'logs'
        self.check("Log directory exists", log_dir.exists(), is_warning=True)
        
        # Django System Checks
        print("\n🔧 Django System Checks:")
        try:
            call_command('check', '--deploy', verbosity=0)
            self.check("Django deployment checks pass", True)
        except Exception as e:
            self.check(f"Django deployment checks failed: {e}", False)
        
        # Print Summary
        print("\n" + "="*50)
        print("📊 SUMMARY:")
        print(f"✅ Checks Passed: {self.checks_passed}")
        print(f"❌ Checks Failed: {self.checks_failed}")
        print(f"⚠️  Warnings: {self.warnings}")
        
        if self.checks_failed == 0:
            print("\n🎉 All critical checks passed! Ready for production.")
            if self.warnings > 0:
                print(f"⚠️  Please review {self.warnings} warning(s) above.")
            return True
        else:
            print(f"\n🚨 {self.checks_failed} critical check(s) failed. Fix these before deploying.")
            return False

def main():
    checker = ProductionChecker()
    success = checker.run_all_checks()
    
    if not success:
        sys.exit(1)
    
    print("\n📋 Next Steps:")
    print("1. Review any warnings above")
    print("2. Test the application thoroughly")
    print("3. Set up monitoring and backups")
    print("4. Configure SSL certificates")
    print("5. Run the deployment script: ./deploy.sh")

if __name__ == '__main__':
    main()
