#!/usr/bin/env python
"""
Script to fix migration issues by cleaning up and recreating properly.
"""
import os
import django
from django.core.management import execute_from_command_line

def clean_and_migrate():
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
    django.setup()
    
    print("🧹 Cleaning up migration issues...")
    
    # Step 1: Create fresh migrations for all apps
    apps = ['authentication', 'catalogue', 'cart', 'coupons', 'orders', 'payments', 'providers']
    
    print("\n📝 Creating fresh migrations...")
    for app in apps:
        try:
            print(f"  Creating migrations for {app}...")
            execute_from_command_line(['manage.py', 'makemigrations', app, '--skip-checks'])
            print(f"  ✅ {app} migrations created")
        except Exception as e:
            print(f"  ⚠️ {app} migrations: {e}")
    
    # Step 2: Migrate each database with only its specific apps
    print("\n🗄️ Applying migrations to databases...")
    
    # Default database - core Django apps + authentication
    print("\n📊 Migrating default database (home_services_auth)...")
    try:
        execute_from_command_line(['manage.py', 'migrate', 'contenttypes', '--database=default', '--skip-checks'])
        execute_from_command_line(['manage.py', 'migrate', 'auth', '--database=default', '--skip-checks'])
        execute_from_command_line(['manage.py', 'migrate', 'admin', '--database=default', '--skip-checks'])
        execute_from_command_line(['manage.py', 'migrate', 'sessions', '--database=default', '--skip-checks'])
        execute_from_command_line(['manage.py', 'migrate', 'authentication', '--database=default', '--skip-checks'])
        print("✅ Default database migrated successfully")
    except Exception as e:
        print(f"❌ Default database error: {e}")
    
    # Individual microservice databases
    microservices = [
        ('catalogue_db', 'catalogue'),
        ('cart_db', 'cart'),
        ('coupons_db', 'coupons'),
        ('orders_db', 'orders'),
        ('payments_db', 'payments'),
        ('providers_db', 'providers')
    ]
    
    for db_name, app_name in microservices:
        print(f"\n📊 Migrating {db_name} for {app_name}...")
        try:
            # First migrate core Django apps to this database
            execute_from_command_line(['manage.py', 'migrate', 'contenttypes', f'--database={db_name}', '--skip-checks'])
            execute_from_command_line(['manage.py', 'migrate', 'auth', f'--database={db_name}', '--skip-checks'])
            
            # Then migrate the specific app
            execute_from_command_line(['manage.py', 'migrate', app_name, f'--database={db_name}', '--skip-checks'])
            print(f"✅ {db_name} migrated successfully")
        except Exception as e:
            print(f"❌ {db_name} error: {e}")
    
    print("\n🎉 Migration fix completed!")
    print("\n📋 Next steps:")
    print("1. Run: python manage.py createsuperuser")
    print("2. Run: python manage.py runserver")

if __name__ == '__main__':
    clean_and_migrate()
