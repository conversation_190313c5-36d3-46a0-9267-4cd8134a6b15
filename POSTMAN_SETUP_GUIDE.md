# 🚀 Postman Setup Guide for Home Services Authentication API

## 📥 **Quick Import (Recommended)**

### **Step 1: Import Collection**
1. Open Postman Desktop App
2. Click **Import** button (top left)
3. Drag and drop `Home_Services_Auth_API.postman_collection.json` file
4. Click **Import**

### **Step 2: Import Environment**
1. Click **Import** button again
2. Drag and drop `Home_Services_Auth_Environment.postman_environment.json` file
3. Click **Import**

### **Step 3: Select Environment**
1. Click the environment dropdown (top right)
2. Select **"Home Services Auth - Development"**

---

## 🔧 **Manual Setup (Alternative)**

### **Create Environment Variables:**

1. **Create New Environment:**
   - Click **Environments** (left sidebar)
   - Click **Create Environment**
   - Name: `Home Services Auth - Development`

2. **Add Variables:**
   | Variable | Initial Value | Current Value |
   |----------|---------------|---------------|
   | `base_url` | `http://localhost:8000` | `http://localhost:8000` |
   | `access_token` | | (leave empty) |
   | `refresh_token` | | (leave empty) |
   | `user_id` | | (leave empty) |
   | `mobile_number` | `+919876543210` | `+919876543210` |
   | `staff_email` | `<EMAIL>` | `<EMAIL>` |

3. **Save Environment**

---

## 🧪 **Testing Workflow**

### **Test 1: Customer Registration & Login**

1. **Register Customer**
   - `POST {{base_url}}/api/auth/register/mobile/`
   - Body: `{"mobile_number": "{{mobile_number}}", "name": "Test User", "user_type": "CUSTOMER"}`

2. **Send OTP**
   - `POST {{base_url}}/api/auth/otp/send/`
   - Body: `{"mobile_number": "{{mobile_number}}"}`

3. **Login with OTP** (Use OTP: 123456 for testing)
   - `POST {{base_url}}/api/auth/login/mobile/`
   - Body: `{"mobile_number": "{{mobile_number}}", "otp": "123456"}`

4. **Get Profile**
   - `GET {{base_url}}/api/auth/profile/`
   - Headers: `Authorization: Bearer {{access_token}}`

### **Test 2: Staff Login**

1. **Staff Login**
   - `POST {{base_url}}/api/auth/login/email/`
   - Body: `{"email": "{{staff_email}}", "password": "your-password"}`

2. **Get User Statistics**
   - `GET {{base_url}}/api/auth/admin/user-stats/`
   - Headers: `Authorization: Bearer {{access_token}}`

---

## 🔑 **Authentication Headers**

For protected endpoints, always include:
```
Authorization: Bearer {{access_token}}
Content-Type: application/json
```

---

## 📝 **Test Scripts (Auto-save tokens)**

Add these test scripts to login endpoints:

**For Mobile Login:**
```javascript
if (pm.response.code === 200) {
    const response = pm.response.json();
    pm.environment.set("access_token", response.tokens.access);
    pm.environment.set("refresh_token", response.tokens.refresh);
    pm.environment.set("user_id", response.user.id);
}
```

**For Staff Login:**
```javascript
if (pm.response.code === 200) {
    const response = pm.response.json();
    pm.environment.set("access_token", response.tokens.access);
    pm.environment.set("refresh_token", response.tokens.refresh);
}
```

---

## 🎯 **Quick Test Endpoints**

### **Health Check:**
- `GET {{base_url}}/admin/` (Should show Django admin login)
- `GET {{base_url}}/api/docs/` (Should show Swagger documentation)

### **Public Endpoints (No Auth Required):**
- `POST {{base_url}}/api/auth/register/mobile/`
- `POST {{base_url}}/api/auth/otp/send/`
- `POST {{base_url}}/api/auth/otp/verify/`
- `POST {{base_url}}/api/auth/login/mobile/`
- `POST {{base_url}}/api/auth/login/email/`

### **Protected Endpoints (Auth Required):**
- `GET {{base_url}}/api/auth/profile/`
- `PUT {{base_url}}/api/auth/profile/`
- `GET {{base_url}}/api/auth/addresses/`
- `POST {{base_url}}/api/auth/addresses/`
- `GET {{base_url}}/api/auth/admin/user-stats/`

---

## ⚠️ **Common Issues & Solutions**

### **401 Unauthorized Error:**
- Check if `access_token` is set in environment
- Try refreshing token or login again

### **400 Bad Request:**
- Check request body format (must be valid JSON)
- Verify required fields are included

### **Connection Refused:**
- Make sure Django server is running: `python manage.py runserver`
- Check `base_url` in environment variables

### **OTP Issues:**
- For testing, use OTP: `123456`
- In production, check MSG91 configuration

---

## 🔄 **Token Refresh**

When access token expires:
1. Use **Refresh Token** endpoint
2. `POST {{base_url}}/api/auth/token/refresh/`
3. Body: `{"refresh": "{{refresh_token}}"}`
4. New access token will be automatically saved

---

## 📊 **Expected Response Codes**

- **200 OK** - Success (GET, PUT requests)
- **201 Created** - Success (POST requests)
- **400 Bad Request** - Validation errors
- **401 Unauthorized** - Authentication required
- **403 Forbidden** - Permission denied
- **404 Not Found** - Endpoint not found
- **500 Internal Server Error** - Server error

---

## 🎉 **You're Ready to Test!**

1. ✅ Import collection and environment
2. ✅ Start Django server: `python manage.py runserver`
3. ✅ Select environment in Postman
4. ✅ Start testing with customer registration
5. ✅ Use auto-saved tokens for protected endpoints

**Happy Testing! 🚀**
