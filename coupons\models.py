from django.db import models
from django.conf import settings
from decimal import Decimal
from django.utils import timezone
from django.core.exceptions import ValidationError
from catalogue.models import Category, Service


class Coupon(models.Model):
    """
    Coupon model for managing promotional offers.
    Supports both percentage and fixed amount discounts with flexible application rules.
    """
    DISCOUNT_TYPE_CHOICES = [
        ('percentage', 'Percentage'),
        ('fixed', 'Fixed Amount'),
    ]
    
    APPLY_TO_CHOICES = [
        ('global', 'Global (All Services)'),
        ('category', 'Specific Categories'),
        ('service', 'Specific Services'),
    ]

    code = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True, null=True)
    discount_type = models.CharField(max_length=10, choices=DISCOUNT_TYPE_CHOICES)
    value = models.DecimalField(max_digits=10, decimal_places=2)
    min_cart_value = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        default=Decimal('0.00'),
        help_text="Minimum cart value required to apply coupon"
    )
    max_discount_value = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        null=True, 
        blank=True,
        help_text="Maximum discount amount for percentage coupons"
    )
    valid_from = models.DateTimeField(default=timezone.now)
    valid_to = models.DateTimeField(null=True, blank=True)
    usage_limit_per_coupon = models.PositiveIntegerField(
        null=True, 
        blank=True,
        help_text="Total number of times this coupon can be used"
    )
    usage_limit_per_user = models.PositiveIntegerField(
        default=1,
        help_text="Number of times a single user can use this coupon"
    )
    is_active = models.BooleanField(default=True)

    applies_to = models.CharField(max_length=10, choices=APPLY_TO_CHOICES, default='global')
    target_categories = models.ManyToManyField(
        Category, 
        blank=True,
        help_text="Categories this coupon applies to"
    )
    target_services = models.ManyToManyField(
        Service, 
        blank=True,
        help_text="Services this coupon applies to"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def clean(self):
        """Validate coupon configuration"""
        if self.applies_to == 'category' and not self.target_categories.exists():
            raise ValidationError("Must specify target categories when applies_to is 'category'.")
        if self.applies_to == 'service' and not self.target_services.exists():
            raise ValidationError("Must specify target services when applies_to is 'service'.")

    def is_valid(self):
        """Check if coupon is currently valid"""
        now = timezone.now()
        if not self.is_active:
            return False
        if self.valid_from > now:
            return False
        if self.valid_to and self.valid_to < now:
            return False
        
        # Check usage limits
        if self.usage_limit_per_coupon:
            total_usage = self.used_coupons.count()
            if total_usage >= self.usage_limit_per_coupon:
                return False
        
        return True

    def calculate_discount(self, amount):
        """Calculate discount amount for given price"""
        if not self.is_valid():
            return Decimal('0.00')
        
        if amount < self.min_cart_value:
            return Decimal('0.00')
        
        if self.discount_type == 'percentage':
            discount = (amount * self.value) / Decimal('100')
            if self.max_discount_value:
                discount = min(discount, self.max_discount_value)
            return discount
        else:  # fixed
            return min(self.value, amount)  # Don't exceed the original amount

    def can_be_used_by_user(self, user):
        """Check if user can use this coupon"""
        if not self.is_valid():
            return False
        
        if user and user.is_authenticated:
            user_usage = self.used_coupons.filter(user=user).count()
            return user_usage < self.usage_limit_per_user
        
        return True

    def __str__(self):
        return self.code


class UsedCoupon(models.Model):
    """
    Track coupon usage to enforce limits.
    """
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE,
        null=True,
        blank=True
    )
    coupon = models.ForeignKey(
        Coupon, 
        on_delete=models.CASCADE,
        related_name='used_coupons'
    )
    cart_total = models.DecimalField(max_digits=10, decimal_places=2)
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2)
    used_at = models.DateTimeField(auto_now_add=True)
    session_key = models.CharField(
        max_length=40, 
        null=True, 
        blank=True,
        help_text="Session key for anonymous users"
    )

    class Meta:
        ordering = ['-used_at']

    def __str__(self):
        user_info = self.user.mobile if self.user else f"Session: {self.session_key}"
        return f"{user_info} used {self.coupon.code} - ₹{self.discount_amount} discount"


class CouponApplication(models.Model):
    """
    Track sequential application of multiple coupons to a cart.
    Supports the requirement for sequential discount calculation.
    """
    cart_id = models.CharField(max_length=50)  # Reference to cart
    coupon = models.ForeignKey(Coupon, on_delete=models.CASCADE)
    order_applied = models.PositiveIntegerField()  # Order in which coupon was applied
    cart_amount_before = models.DecimalField(max_digits=10, decimal_places=2)
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2)
    cart_amount_after = models.DecimalField(max_digits=10, decimal_places=2)
    applied_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order_applied']
        unique_together = ('cart_id', 'coupon')

    def __str__(self):
        return f"Cart {self.cart_id} - {self.coupon.code} (Order: {self.order_applied})"
