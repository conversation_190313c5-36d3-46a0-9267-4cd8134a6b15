# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: Mongolian translation\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2009-09-23 14:44+0200\n"
"PO-Revision-Date: 2014-03-04 23:32+0800\n"
"Last-Translator: Bayarkhuu Bataa <<EMAIL>>\n"
"Language-Team: <PERSON>ark<PERSON><PERSON> <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.5.7\n"

#: __init__.py:34
#, python-format
msgid "The model %s has already been registered."
msgstr "%s модел аль хэдийнэ бүртгэгдсэн."

#: forms.py:41
msgid "First child"
msgstr "Эхний хүү"

#: forms.py:42
msgid "Last child"
msgstr "Сүүлийн хүү"

#: forms.py:43
msgid "Left sibling"
msgstr "Зүүн ах дүүс"

#: forms.py:44
msgid "Right sibling"
msgstr "Баруун ах дүүс"

#: managers.py:121
msgid "Cannot insert a node which has already been saved."
msgstr "Өмнө нь орсон зангилааг дахиж оруулж болохгүй."

#: managers.py:306 managers.py:480 managers.py:516 managers.py:673
#, python-format
msgid "An invalid position was given: %s."
msgstr "Буруу байрлал: %s."

#: managers.py:466 managers.py:653
msgid "A node may not be made a sibling of itself."
msgstr "Зангилаа өөрийнхөө ах дүү байж болохгүй."

#: managers.py:632 managers.py:753
msgid "A node may not be made a child of itself."
msgstr "Зангилаа өөрийнхөө хүү байж болохгүй."

#: managers.py:634 managers.py:755
msgid "A node may not be made a child of any of its descendants."
msgstr "Зангилаа өөрийнхөө үр садын хүү байж болохгүй."

#: managers.py:655
msgid "A node may not be made a sibling of any of its descendants."
msgstr "Зангилаа өөрийнхөө үр садын ах дүү байж болохгүй."

#: templatetags/mptt_tags.py:23
#, python-format
msgid "full_tree_for_model tag was given an invalid model: %s"
msgstr "full_tree_for_model нь буруу моделд тодорхойлогдсон байна: %s"

#: templatetags/mptt_tags.py:44
#, python-format
msgid "drilldown_tree_for_node tag was given an invalid model: %s"
msgstr "drilldown_tree_for_node нь буруу моделд тодорхойлогдсон байна: %s"

#: templatetags/mptt_tags.py:48
#, python-format
msgid "drilldown_tree_for_node tag was given an invalid model field: %s"
msgstr "drilldown_tree_for_node нь буруу моделд тодорхойлогдсон байна: %s"

#: templatetags/mptt_tags.py:72
#, python-format
msgid "%s tag requires three arguments"
msgstr "%s нь гурван аргумент шаардана"

#: templatetags/mptt_tags.py:74 templatetags/mptt_tags.py:125
#, python-format
msgid "second argument to %s tag must be 'as'"
msgstr "%s руу дамжуулах хоёрдох аргумент нь  'as байх ёстой'"

#: templatetags/mptt_tags.py:123
#, python-format
msgid "%s tag requires either three, seven or eight arguments"
msgstr "%s нь гурав, долоо эсвэл найман аргумент шаардана"

#: templatetags/mptt_tags.py:128
#, python-format
msgid "if seven arguments are given, fourth argument to %s tag must be 'with'"
msgstr "%s руу долоон аргумент дамжуулсан бол 4 дэх нь 'with' байх ёстой"

#: templatetags/mptt_tags.py:130
#, python-format
msgid "if seven arguments are given, sixth argument to %s tag must be 'in'"
msgstr "%s руу долоон аргумент дамжуулсан бол 6 дэх нь 'in' байх ёстой"

#: templatetags/mptt_tags.py:134
#, python-format
msgid ""
"if eight arguments are given, fourth argument to %s tag must be 'cumulative'"
msgstr "%s руу найман аргумент дамжуулсан бол 4 дэх нь 'cumulative' байх ёстой"

#: templatetags/mptt_tags.py:136
#, python-format
msgid "if eight arguments are given, fifth argument to %s tag must be 'count'"
msgstr "%s руу найман аргумент дамжуулсан бол 5 дахь нь 'count' байх ёстой"

#: templatetags/mptt_tags.py:138
#, python-format
msgid "if eight arguments are given, seventh argument to %s tag must be 'in'"
msgstr "%s руу найман аргумент дамжуулсан бол 7 дахь нь 'in' байх ёстой"
