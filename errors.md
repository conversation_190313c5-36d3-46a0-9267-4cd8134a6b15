(venv) C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend>python manage.py migrate --database=default
C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\rest_framework_simplejwt\__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Operations to perform:
  Apply all migrations: admin, auth, authentication, cart, catalogue, contenttypes, coupons, orders, payments, providers, sessions
Running migrations:
  Applying contenttypes.0001_initial... OK
  Applying contenttypes.0002_remove_content_type_name... OK
  Applying auth.0001_initial... OK
  Applying auth.0002_alter_permission_name_max_length... OK
  Applying auth.0003_alter_user_email_max_length... OK
  Applying auth.0004_alter_user_username_opts... OK
  Applying auth.0005_alter_user_last_login_null... OK
  Applying auth.0006_require_contenttypes_0002... OK
  Applying auth.0007_alter_validators_add_error_messages... OK
  Applying auth.0008_alter_user_username_max_length... OK
  Applying auth.0009_alter_user_last_name_max_length... OK
  Applying auth.0010_alter_group_name_max_length... OK
  Applying auth.0011_update_proxy_permissions... OK
  Applying auth.0012_alter_user_first_name_max_length... OK
  Applying authentication.0001_initial...Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 87, in _execute
    return self.cursor.execute(sql)
           ~~~~~~~~~~~~~~~~~~~^^^^^
psycopg2.errors.UndefinedTable: relation "auth_group" does not exist


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\manage.py", line 22, in <module>
    main()
    ~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\manage.py", line 18, in main
    execute_from_command_line(sys.argv)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\__init__.py", line 442, in execute_from_command_line
    utility.execute()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\__init__.py", line 436, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\base.py", line 412, in run_from_argv
    self.execute(*args, **cmd_options)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\base.py", line 458, in execute
    output = self.handle(*args, **options)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\base.py", line 106, in wrapper
    res = handle_func(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\commands\migrate.py", line 356, in handle
    post_migrate_state = executor.migrate(
        targets,
    ...<3 lines>...
        fake_initial=fake_initial,
    )
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\migrations\executor.py", line 135, in migrate
    state = self._migrate_all_forwards(
        state, plan, full_plan, fake=fake, fake_initial=fake_initial
    )
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\migrations\executor.py", line 167, in _migrate_all_forwards
    state = self.apply_migration(
        state, migration, fake=fake, fake_initial=fake_initial
    )
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\migrations\executor.py", line 249, in apply_migration
    with self.connection.schema_editor(
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        atomic=migration.atomic
        ^^^^^^^^^^^^^^^^^^^^^^^
    ) as schema_editor:
    ^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\base\schema.py", line 166, in __exit__
    self.execute(sql)
    ~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\postgresql\schema.py", line 48, in execute
    return super().execute(sql, None)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\base\schema.py", line 201, in execute
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 87, in _execute
    return self.cursor.execute(sql)
           ~~~~~~~~~~~~~~~~~~~^^^^^
django.db.utils.ProgrammingError: relation "auth_group" does not exist


(venv) C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend>
(venv) C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend># Apply other service migrations to their respective databases
'#' is not recognized as an internal or external command,
operable program or batch file.

(venv) C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend>python manage.py migrate --database=catalogue_db
C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\rest_framework_simplejwt\__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Operations to perform:
  Apply all migrations: admin, auth, authentication, cart, catalogue, contenttypes, coupons, orders, payments, providers, sessions
Running migrations:
  Applying contenttypes.0001_initial... OK
  Applying contenttypes.0002_remove_content_type_name... OK
  Applying auth.0001_initial... OK
  Applying auth.0002_alter_permission_name_max_length... OK
  Applying auth.0003_alter_user_email_max_length... OK
  Applying auth.0004_alter_user_username_opts... OK
  Applying auth.0005_alter_user_last_login_null... OK
  Applying auth.0006_require_contenttypes_0002... OK
  Applying auth.0007_alter_validators_add_error_messages... OK
  Applying auth.0008_alter_user_username_max_length... OK
  Applying auth.0009_alter_user_last_name_max_length... OK
  Applying auth.0010_alter_group_name_max_length... OK
  Applying auth.0011_update_proxy_permissions... OK
  Applying auth.0012_alter_user_first_name_max_length... OK
  Applying authentication.0001_initial... OK
  Applying admin.0001_initial... OK
  Applying admin.0002_logentry_remove_auto_add... OK
  Applying admin.0003_logentry_add_action_flag_choices... OK
  Applying catalogue.0001_initial... OK
  Applying cart.0001_initial... OK
  Applying coupons.0001_initial... OK
  Applying orders.0001_initial... OK
  Applying payments.0001_initial... OK
  Applying providers.0001_initial... OK
  Applying sessions.0001_initial... OK

(venv) C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend>python manage.py migrate --database=cart_db
C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\rest_framework_simplejwt\__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Operations to perform:
  Apply all migrations: admin, auth, authentication, cart, catalogue, contenttypes, coupons, orders, payments, providers, sessions
Running migrations:
  Applying contenttypes.0001_initial... OK
  Applying contenttypes.0002_remove_content_type_name... OK
  Applying auth.0001_initial... OK
  Applying auth.0002_alter_permission_name_max_length... OK
  Applying auth.0003_alter_user_email_max_length... OK
  Applying auth.0004_alter_user_username_opts... OK
  Applying auth.0005_alter_user_last_login_null... OK
  Applying auth.0006_require_contenttypes_0002... OK
  Applying auth.0007_alter_validators_add_error_messages... OK
  Applying auth.0008_alter_user_username_max_length... OK
  Applying auth.0009_alter_user_last_name_max_length... OK
  Applying auth.0010_alter_group_name_max_length... OK
  Applying auth.0011_update_proxy_permissions... OK
  Applying auth.0012_alter_user_first_name_max_length... OK
  Applying authentication.0001_initial... OK
  Applying admin.0001_initial... OK
  Applying admin.0002_logentry_remove_auto_add... OK
  Applying admin.0003_logentry_add_action_flag_choices... OK
  Applying catalogue.0001_initial... OK
  Applying cart.0001_initial...Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 87, in _execute
    return self.cursor.execute(sql)
           ~~~~~~~~~~~~~~~~~~~^^^^^
psycopg2.errors.UndefinedTable: relation "auth_user" does not exist


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\manage.py", line 22, in <module>
    main()
    ~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\manage.py", line 18, in main
    execute_from_command_line(sys.argv)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\__init__.py", line 442, in execute_from_command_line
    utility.execute()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\__init__.py", line 436, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\base.py", line 412, in run_from_argv
    self.execute(*args, **cmd_options)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\base.py", line 458, in execute
    output = self.handle(*args, **options)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\base.py", line 106, in wrapper
    res = handle_func(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\commands\migrate.py", line 356, in handle
    post_migrate_state = executor.migrate(
        targets,
    ...<3 lines>...
        fake_initial=fake_initial,
    )
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\migrations\executor.py", line 135, in migrate
    state = self._migrate_all_forwards(
        state, plan, full_plan, fake=fake, fake_initial=fake_initial
    )
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\migrations\executor.py", line 167, in _migrate_all_forwards
    state = self.apply_migration(
        state, migration, fake=fake, fake_initial=fake_initial
    )
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\migrations\executor.py", line 249, in apply_migration
    with self.connection.schema_editor(
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        atomic=migration.atomic
        ^^^^^^^^^^^^^^^^^^^^^^^
    ) as schema_editor:
    ^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\base\schema.py", line 166, in __exit__
    self.execute(sql)
    ~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\postgresql\schema.py", line 48, in execute
    return super().execute(sql, None)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\base\schema.py", line 201, in execute
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 87, in _execute
    return self.cursor.execute(sql)
           ~~~~~~~~~~~~~~~~~~~^^^^^
django.db.utils.ProgrammingError: relation "auth_user" does not exist


(venv) C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend>python manage.py migrate --database=coupons_db
C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\rest_framework_simplejwt\__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Operations to perform:
  Apply all migrations: admin, auth, authentication, cart, catalogue, contenttypes, coupons, orders, payments, providers, sessions
Running migrations:
  Applying contenttypes.0001_initial... OK
  Applying contenttypes.0002_remove_content_type_name... OK
  Applying auth.0001_initial... OK
  Applying auth.0002_alter_permission_name_max_length... OK
  Applying auth.0003_alter_user_email_max_length... OK
  Applying auth.0004_alter_user_username_opts... OK
  Applying auth.0005_alter_user_last_login_null... OK
  Applying auth.0006_require_contenttypes_0002... OK
  Applying auth.0007_alter_validators_add_error_messages... OK
  Applying auth.0008_alter_user_username_max_length... OK
  Applying auth.0009_alter_user_last_name_max_length... OK
  Applying auth.0010_alter_group_name_max_length... OK
  Applying auth.0011_update_proxy_permissions... OK
  Applying auth.0012_alter_user_first_name_max_length... OK
  Applying authentication.0001_initial... OK
  Applying admin.0001_initial... OK
  Applying admin.0002_logentry_remove_auto_add... OK
  Applying admin.0003_logentry_add_action_flag_choices... OK
  Applying catalogue.0001_initial... OK
  Applying cart.0001_initial... OK
  Applying coupons.0001_initial...Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 87, in _execute
    return self.cursor.execute(sql)
           ~~~~~~~~~~~~~~~~~~~^^^^^
psycopg2.errors.UndefinedTable: relation "catalogue_category" does not exist


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\manage.py", line 22, in <module>
    main()
    ~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\manage.py", line 18, in main
    execute_from_command_line(sys.argv)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\__init__.py", line 442, in execute_from_command_line
    utility.execute()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\__init__.py", line 436, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\base.py", line 412, in run_from_argv
    self.execute(*args, **cmd_options)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\base.py", line 458, in execute
    output = self.handle(*args, **options)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\base.py", line 106, in wrapper
    res = handle_func(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\commands\migrate.py", line 356, in handle
    post_migrate_state = executor.migrate(
        targets,
    ...<3 lines>...
        fake_initial=fake_initial,
    )
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\migrations\executor.py", line 135, in migrate
    state = self._migrate_all_forwards(
        state, plan, full_plan, fake=fake, fake_initial=fake_initial
    )
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\migrations\executor.py", line 167, in _migrate_all_forwards
    state = self.apply_migration(
        state, migration, fake=fake, fake_initial=fake_initial
    )
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\migrations\executor.py", line 249, in apply_migration
    with self.connection.schema_editor(
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        atomic=migration.atomic
        ^^^^^^^^^^^^^^^^^^^^^^^
    ) as schema_editor:
    ^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\base\schema.py", line 166, in __exit__
    self.execute(sql)
    ~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\postgresql\schema.py", line 48, in execute
    return super().execute(sql, None)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\base\schema.py", line 201, in execute
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 87, in _execute
    return self.cursor.execute(sql)
           ~~~~~~~~~~~~~~~~~~~^^^^^
django.db.utils.ProgrammingError: relation "catalogue_category" does not exist


(venv) C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend>python manage.py migrate --database=orders_db
C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\rest_framework_simplejwt\__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Operations to perform:
  Apply all migrations: admin, auth, authentication, cart, catalogue, contenttypes, coupons, orders, payments, providers, sessions
Running migrations:
  Applying contenttypes.0001_initial... OK
  Applying contenttypes.0002_remove_content_type_name... OK
  Applying auth.0001_initial... OK
  Applying auth.0002_alter_permission_name_max_length... OK
  Applying auth.0003_alter_user_email_max_length... OK
  Applying auth.0004_alter_user_username_opts... OK
  Applying auth.0005_alter_user_last_login_null... OK
  Applying auth.0006_require_contenttypes_0002... OK
  Applying auth.0007_alter_validators_add_error_messages... OK
  Applying auth.0008_alter_user_username_max_length... OK
  Applying auth.0009_alter_user_last_name_max_length... OK
  Applying auth.0010_alter_group_name_max_length... OK
  Applying auth.0011_update_proxy_permissions... OK
  Applying auth.0012_alter_user_first_name_max_length... OK
  Applying authentication.0001_initial... OK
  Applying admin.0001_initial... OK
  Applying admin.0002_logentry_remove_auto_add... OK
  Applying admin.0003_logentry_add_action_flag_choices... OK
  Applying catalogue.0001_initial... OK
  Applying cart.0001_initial... OK
  Applying coupons.0001_initial... OK
  Applying orders.0001_initial...Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 87, in _execute
    return self.cursor.execute(sql)
           ~~~~~~~~~~~~~~~~~~~^^^^^
psycopg2.errors.UndefinedTable: relation "auth_user" does not exist


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\manage.py", line 22, in <module>
    main()
    ~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\manage.py", line 18, in main
    execute_from_command_line(sys.argv)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\__init__.py", line 442, in execute_from_command_line
    utility.execute()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\__init__.py", line 436, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\base.py", line 412, in run_from_argv
    self.execute(*args, **cmd_options)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\base.py", line 458, in execute
    output = self.handle(*args, **options)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\base.py", line 106, in wrapper
    res = handle_func(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\commands\migrate.py", line 356, in handle
    post_migrate_state = executor.migrate(
        targets,
    ...<3 lines>...
        fake_initial=fake_initial,
    )
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\migrations\executor.py", line 135, in migrate
    state = self._migrate_all_forwards(
        state, plan, full_plan, fake=fake, fake_initial=fake_initial
    )
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\migrations\executor.py", line 167, in _migrate_all_forwards
    state = self.apply_migration(
        state, migration, fake=fake, fake_initial=fake_initial
    )
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\migrations\executor.py", line 249, in apply_migration
    with self.connection.schema_editor(
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        atomic=migration.atomic
        ^^^^^^^^^^^^^^^^^^^^^^^
    ) as schema_editor:
    ^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\base\schema.py", line 166, in __exit__
    self.execute(sql)
    ~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\postgresql\schema.py", line 48, in execute
    return super().execute(sql, None)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\base\schema.py", line 201, in execute
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 87, in _execute
    return self.cursor.execute(sql)
           ~~~~~~~~~~~~~~~~~~~^^^^^
django.db.utils.ProgrammingError: relation "auth_user" does not exist


(venv) C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend>python manage.py migrate --database=payments_db
C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\rest_framework_simplejwt\__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Operations to perform:
  Apply all migrations: admin, auth, authentication, cart, catalogue, contenttypes, coupons, orders, payments, providers, sessions
Running migrations:
  Applying contenttypes.0001_initial... OK
  Applying contenttypes.0002_remove_content_type_name... OK
  Applying auth.0001_initial... OK
  Applying auth.0002_alter_permission_name_max_length... OK
  Applying auth.0003_alter_user_email_max_length... OK
  Applying auth.0004_alter_user_username_opts... OK
  Applying auth.0005_alter_user_last_login_null... OK
  Applying auth.0006_require_contenttypes_0002... OK
  Applying auth.0007_alter_validators_add_error_messages... OK
  Applying auth.0008_alter_user_username_max_length... OK
  Applying auth.0009_alter_user_last_name_max_length... OK
  Applying auth.0010_alter_group_name_max_length... OK
  Applying auth.0011_update_proxy_permissions... OK
  Applying auth.0012_alter_user_first_name_max_length... OK
  Applying authentication.0001_initial... OK
  Applying admin.0001_initial... OK
  Applying admin.0002_logentry_remove_auto_add... OK
  Applying admin.0003_logentry_add_action_flag_choices... OK
  Applying catalogue.0001_initial... OK
  Applying cart.0001_initial... OK
  Applying coupons.0001_initial... OK
  Applying orders.0001_initial... OK
  Applying payments.0001_initial...Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 87, in _execute
    return self.cursor.execute(sql)
           ~~~~~~~~~~~~~~~~~~~^^^^^
psycopg2.errors.UndefinedTable: relation "auth_user" does not exist


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\manage.py", line 22, in <module>
    main()
    ~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\manage.py", line 18, in main
    execute_from_command_line(sys.argv)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\__init__.py", line 442, in execute_from_command_line
    utility.execute()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\__init__.py", line 436, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\base.py", line 412, in run_from_argv
    self.execute(*args, **cmd_options)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\base.py", line 458, in execute
    output = self.handle(*args, **options)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\base.py", line 106, in wrapper
    res = handle_func(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\commands\migrate.py", line 356, in handle
    post_migrate_state = executor.migrate(
        targets,
    ...<3 lines>...
        fake_initial=fake_initial,
    )
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\migrations\executor.py", line 135, in migrate
    state = self._migrate_all_forwards(
        state, plan, full_plan, fake=fake, fake_initial=fake_initial
    )
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\migrations\executor.py", line 167, in _migrate_all_forwards
    state = self.apply_migration(
        state, migration, fake=fake, fake_initial=fake_initial
    )
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\migrations\executor.py", line 249, in apply_migration
    with self.connection.schema_editor(
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        atomic=migration.atomic
        ^^^^^^^^^^^^^^^^^^^^^^^
    ) as schema_editor:
    ^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\base\schema.py", line 166, in __exit__
    self.execute(sql)
    ~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\postgresql\schema.py", line 48, in execute
    return super().execute(sql, None)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\base\schema.py", line 201, in execute
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 87, in _execute
    return self.cursor.execute(sql)
           ~~~~~~~~~~~~~~~~~~~^^^^^
django.db.utils.ProgrammingError: relation "auth_user" does not exist


(venv) C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend>python manage.py migrate --database=providers_db
C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\rest_framework_simplejwt\__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Operations to perform:
  Apply all migrations: admin, auth, authentication, cart, catalogue, contenttypes, coupons, orders, payments, providers, sessions
Running migrations:
  Applying contenttypes.0001_initial... OK
  Applying contenttypes.0002_remove_content_type_name... OK
  Applying auth.0001_initial... OK
  Applying auth.0002_alter_permission_name_max_length... OK
  Applying auth.0003_alter_user_email_max_length... OK
  Applying auth.0004_alter_user_username_opts... OK
  Applying auth.0005_alter_user_last_login_null... OK
  Applying auth.0006_require_contenttypes_0002... OK
  Applying auth.0007_alter_validators_add_error_messages... OK
  Applying auth.0008_alter_user_username_max_length... OK
  Applying auth.0009_alter_user_last_name_max_length... OK
  Applying auth.0010_alter_group_name_max_length... OK
  Applying auth.0011_update_proxy_permissions... OK
  Applying auth.0012_alter_user_first_name_max_length... OK
  Applying authentication.0001_initial... OK
  Applying admin.0001_initial... OK
  Applying admin.0002_logentry_remove_auto_add... OK
  Applying admin.0003_logentry_add_action_flag_choices... OK
  Applying catalogue.0001_initial... OK
  Applying cart.0001_initial... OK
  Applying coupons.0001_initial... OK
  Applying orders.0001_initial... OK
  Applying payments.0001_initial... OK
  Applying providers.0001_initial...Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 87, in _execute
    return self.cursor.execute(sql)
           ~~~~~~~~~~~~~~~~~~~^^^^^
psycopg2.errors.UndefinedTable: relation "auth_user" does not exist


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\manage.py", line 22, in <module>
    main()
    ~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\manage.py", line 18, in main
    execute_from_command_line(sys.argv)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\__init__.py", line 442, in execute_from_command_line
    utility.execute()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\__init__.py", line 436, in execute
    self.fetch_command(subcommand).run_from_argv(self.argv)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\base.py", line 412, in run_from_argv
    self.execute(*args, **cmd_options)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\base.py", line 458, in execute
    output = self.handle(*args, **options)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\base.py", line 106, in wrapper
    res = handle_func(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\management\commands\migrate.py", line 356, in handle
    post_migrate_state = executor.migrate(
        targets,
    ...<3 lines>...
        fake_initial=fake_initial,
    )
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\migrations\executor.py", line 135, in migrate
    state = self._migrate_all_forwards(
        state, plan, full_plan, fake=fake, fake_initial=fake_initial
    )
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\migrations\executor.py", line 167, in _migrate_all_forwards
    state = self.apply_migration(
        state, migration, fake=fake, fake_initial=fake_initial
    )
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\migrations\executor.py", line 249, in apply_migration
    with self.connection.schema_editor(
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        atomic=migration.atomic
        ^^^^^^^^^^^^^^^^^^^^^^^
    ) as schema_editor:
    ^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\base\schema.py", line 166, in __exit__
    self.execute(sql)
    ~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\postgresql\schema.py", line 48, in execute
    return super().execute(sql, None)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\base\schema.py", line 201, in execute
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\db\backends\utils.py", line 87, in _execute
    return self.cursor.execute(sql)
           ~~~~~~~~~~~~~~~~~~~^^^^^
django.db.utils.ProgrammingError: relation "auth_user" does not exist


(venv) C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend>