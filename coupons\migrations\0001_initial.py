# Generated by Django 4.2.7 on 2025-06-13 10:19

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('catalogue', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Coupon',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('discount_type', models.CharField(choices=[('percentage', 'Percentage'), ('fixed', 'Fixed Amount')], max_length=10)),
                ('value', models.DecimalField(decimal_places=2, max_digits=10)),
                ('min_cart_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Minimum cart value required to apply coupon', max_digits=10)),
                ('max_discount_value', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum discount amount for percentage coupons', max_digits=10, null=True)),
                ('valid_from', models.DateTimeField(default=django.utils.timezone.now)),
                ('valid_to', models.DateTimeField(blank=True, null=True)),
                ('usage_limit_per_coupon', models.PositiveIntegerField(blank=True, help_text='Total number of times this coupon can be used', null=True)),
                ('usage_limit_per_user', models.PositiveIntegerField(default=1, help_text='Number of times a single user can use this coupon')),
                ('is_active', models.BooleanField(default=True)),
                ('applies_to', models.CharField(choices=[('global', 'Global (All Services)'), ('category', 'Specific Categories'), ('service', 'Specific Services')], default='global', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('target_categories', models.ManyToManyField(blank=True, help_text='Categories this coupon applies to', to='catalogue.category')),
                ('target_services', models.ManyToManyField(blank=True, help_text='Services this coupon applies to', to='catalogue.service')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UsedCoupon',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cart_total', models.DecimalField(decimal_places=2, max_digits=10)),
                ('discount_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('used_at', models.DateTimeField(auto_now_add=True)),
                ('session_key', models.CharField(blank=True, help_text='Session key for anonymous users', max_length=40, null=True)),
                ('coupon', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='used_coupons', to='coupons.coupon')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-used_at'],
            },
        ),
        migrations.CreateModel(
            name='CouponApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cart_id', models.CharField(max_length=50)),
                ('order_applied', models.PositiveIntegerField()),
                ('cart_amount_before', models.DecimalField(decimal_places=2, max_digits=10)),
                ('discount_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('cart_amount_after', models.DecimalField(decimal_places=2, max_digits=10)),
                ('applied_at', models.DateTimeField(auto_now_add=True)),
                ('coupon', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='coupons.coupon')),
            ],
            options={
                'ordering': ['order_applied'],
                'unique_together': {('cart_id', 'coupon')},
            },
        ),
    ]
