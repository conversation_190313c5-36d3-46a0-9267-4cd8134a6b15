#!/usr/bin/env python
"""
Migrate with improved database router - no database reset required.
"""
import os
import django
from django.core.management import execute_from_command_line

def migrate_with_router():
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
    django.setup()
    
    print("🚀 Running migrations with improved database router...")
    
    # Step 1: Migrate core Django apps to default database only
    print("\n🔧 Step 1: Migrating core apps to default database...")
    core_apps = ['contenttypes', 'auth', 'admin', 'sessions', 'authentication']
    
    for app in core_apps:
        try:
            print(f"  🔧 Migrating {app} to default...")
            execute_from_command_line([
                'manage.py', 'migrate', app,
                '--database=default',
                '--skip-checks',
                '--verbosity=1'
            ])
            print(f"  ✅ {app} migrated to default")
        except Exception as e:
            print(f"  ⚠️ {app} error: {str(e)[:100]}...")
    
    # Step 2: Run a global migrate command. The router will direct custom apps.
    print("\n🔧 Step 2: Running global migrate for custom apps (router will direct)...")
    try:
        execute_from_command_line([
            'manage.py', 'migrate',
            '--skip-checks',
            '--verbosity=1'
        ])
        print("  ✅ Global migrate completed.")
    except Exception as e:
        print(f"  ❌ Global migrate error: {e}")

    print("\n🎉 Migration completed with improved router!")
    print("\n📋 Database Structure:")
    print("  🗄️ home_services_auth: authentication + core Django apps")
    print("  🗄️ home_services_catalogue: catalogue app only")
    print("  🗄️ home_services_cart: cart app only")
    print("  🗄️ home_services_coupons: coupons app only")
    print("  🗄️ home_services_orders: orders app only")
    print("  🗄️ home_services_payments: payments app only")
    print("  🗄️ home_services_providers: providers app only")
    
    print("\n📋 Next steps:")
    print("1. Run: python verify_databases.py")
    print("2. Run: python manage.py createsuperuser")
    print("3. Run: python manage.py runserver")

if __name__ == '__main__':
    migrate_with_router()
