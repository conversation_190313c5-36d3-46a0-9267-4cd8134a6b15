"""
Database router for microservices architecture.
Routes models to their respective databases.
"""

class DatabaseRouter:
    """
    A router to control all database operations on models for different
    microservices to ensure they use their dedicated databases.
    """

    # Define which apps use which databases
    route_app_labels = {
        'authentication': 'default',  # Uses default database
        'catalogue': 'catalogue_db',
        'cart': 'cart_db', 
        'coupons': 'coupons_db',
        'orders': 'orders_db',
        'payments': 'payments_db',
        'providers': 'providers_db',
    }

    def db_for_read(self, model, **hints):
        """Suggest the database to read from."""
        if model._meta.app_label in self.route_app_labels:
            return self.route_app_labels[model._meta.app_label]
        # Core Django apps and any other unrouted apps go to default
        return 'default'

    def db_for_write(self, model, **hints):
        """Suggest the database to write to."""
        if model._meta.app_label in self.route_app_labels:
            return self.route_app_labels[model._meta.app_label]
        # Core Django apps and any other unrouted apps go to default
        return 'default'

    def allow_relation(self, obj1, obj2, **hints):
        """
        Allow relations if both objects are in the same database.
        Crucially, allow relations between custom apps and core Django apps in the 'default' database.
        """
        db_obj1 = self.route_app_labels.get(obj1._meta.app_label, 'default')
        db_obj2 = self.route_app_labels.get(obj2._meta.app_label, 'default')

        # If both objects are in the same database, allow the relation
        if db_obj1 == db_obj2:
            return True

        # Allow relations between specific apps and the 'default' database
        # This allows custom apps (which are in separate DBs) to have foreign keys
        # to models in the 'default' DB (like User, ContentType).

        # Apps that are explicitly routed to a specific database (your custom apps)
        routed_app_labels = set(self.route_app_labels.keys())

        # Core Django apps that reside in the 'default' database
        default_db_core_apps = {'auth', 'contenttypes', 'admin', 'sessions', 'authentication'}

        # Check if one object is a routed app model and the other is a core Django app model in 'default'
        if obj1._meta.app_label in routed_app_labels and obj2._meta.app_label in default_db_core_apps:
            return db_obj1 in self.route_app_labels.values() and db_obj2 == 'default'

        if obj2._meta.app_label in routed_app_labels and obj1._meta.app_label in default_db_core_apps:
            return db_obj2 in self.route_app_labels.values() and db_obj1 == 'default'

        return False  # Disallow all other relations

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """
        Make sure core Django apps only appear in the 'default' database.
        Custom apps are routed to their specific database.
        """
        # If the app is one of our explicitly routed custom apps
        if app_label in self.route_app_labels:
            return db == self.route_app_labels[app_label]

        # If the app is one of Django's core apps (auth, contenttypes, admin, sessions)
        # or any other app not explicitly routed, it should only migrate to the 'default' database.
        elif db == 'default':
            return True

        # Don't allow any other apps to migrate to non-default databases unless specifically routed
        return False
