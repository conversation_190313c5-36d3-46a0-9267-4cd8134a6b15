"""
Database router for microservices architecture.
Routes models to their respective databases.
"""

class DatabaseRouter:
    """
    A router to control all database operations on models for different
    microservices to ensure they use their dedicated databases.
    """

    # Define which apps use which databases
    route_app_labels = {
        'authentication': 'default',  # Uses default database
        'catalogue': 'catalogue_db',
        'cart': 'cart_db', 
        'coupons': 'coupons_db',
        'orders': 'orders_db',
        'payments': 'payments_db',
        'providers': 'providers_db',
    }

    def db_for_read(self, model, **hints):
        """Suggest the database to read from."""
        if model._meta.app_label in self.route_app_labels:
            return self.route_app_labels[model._meta.app_label]
        return None

    def db_for_write(self, model, **hints):
        """Suggest the database to write to."""
        if model._meta.app_label in self.route_app_labels:
            return self.route_app_labels[model._meta.app_label]
        return None

    def allow_relation(self, obj1, obj2, **hints):
        """Allow relations if models are in the same app."""
        db_set = {'default', 'catalogue_db', 'cart_db', 'coupons_db', 'orders_db', 'payments_db', 'providers_db'}
        if obj1._state.db in db_set and obj2._state.db in db_set:
            return True
        return None

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """Ensure that certain apps' models get created on the right database."""
        # Core Django apps should go to ALL databases (needed for foreign keys)
        core_apps = ['admin', 'auth', 'contenttypes', 'sessions']

        if app_label in core_apps:
            return True  # Allow core apps on all databases

        # Our custom apps should go to their designated databases
        if app_label in self.route_app_labels:
            return db == self.route_app_labels[app_label]

        # Authentication app should only go to default database
        if app_label == 'authentication':
            return db == 'default'

        # Don't allow other unknown apps on microservice databases
        if db in self.route_app_labels.values() and app_label not in self.route_app_labels:
            return False

        # Default behavior for other apps
        return db == 'default'
