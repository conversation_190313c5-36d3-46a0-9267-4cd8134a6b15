from rest_framework import serializers
from decimal import Decimal
from .models import PaymentTransaction, RazorpayPayment, CODPayment, PaymentRefund, PaymentWebhook


class PaymentTransactionSerializer(serializers.ModelSerializer):
    """
    Serializer for PaymentTransaction model.
    """
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    user_mobile = serializers.CharField(source='user.mobile', read_only=True)

    class Meta:
        model = PaymentTransaction
        fields = [
            'id', 'transaction_id', 'order_id', 'order_number',
            'user', 'user_name', 'user_mobile', 'payment_method',
            'amount', 'currency', 'status', 'gateway_transaction_id',
            'gateway_payment_id', 'gateway_signature', 'failure_reason',
            'failure_code', 'refund_amount', 'refund_reason',
            'created_at', 'updated_at', 'completed_at', 'refunded_at'
        ]
        read_only_fields = [
            'id', 'transaction_id', 'created_at', 'updated_at', 'completed_at'
        ]


class InitiatePaymentSerializer(serializers.Serializer):
    """
    Serializer for initiating payment.
    """
    order_id = serializers.CharField()
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    payment_method = serializers.ChoiceField(
        choices=[('razorpay', 'Razorpay'), ('cod', 'Cash on Delivery')]
    )
    currency = serializers.CharField(default='INR', max_length=3)

    def validate_amount(self, value):
        if value <= 0:
            raise serializers.ValidationError("Amount must be greater than zero")
        return value


class PaymentVerificationSerializer(serializers.Serializer):
    """
    Serializer for payment verification.
    """
    transaction_id = serializers.CharField()
    razorpay_payment_id = serializers.CharField(required=False)
    razorpay_order_id = serializers.CharField(required=False)
    razorpay_signature = serializers.CharField(required=False)


class CODConfirmationSerializer(serializers.Serializer):
    """
    Serializer for COD payment confirmation.
    """
    transaction_id = serializers.CharField()
    collected_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    collection_notes = serializers.CharField(max_length=500, required=False)


class RefundSerializer(serializers.ModelSerializer):
    """
    Serializer for PaymentRefund model.
    """
    transaction_id = serializers.CharField(source='transaction.transaction_id', read_only=True)
    order_number = serializers.CharField(source='transaction.order_number', read_only=True)
    initiated_by_name = serializers.CharField(source='initiated_by.get_full_name', read_only=True)

    class Meta:
        model = PaymentRefund
        fields = [
            'id', 'refund_id', 'transaction', 'transaction_id', 'order_number',
            'amount', 'reason', 'status', 'gateway_refund_id',
            'initiated_by', 'initiated_by_name', 'processed_at',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'refund_id', 'created_at', 'updated_at']


class InitiateRefundSerializer(serializers.Serializer):
    """
    Serializer for initiating refund.
    """
    transaction_id = serializers.CharField()
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    reason = serializers.CharField(max_length=500)

    def validate_amount(self, value):
        if value <= 0:
            raise serializers.ValidationError("Refund amount must be greater than zero")
        return value


class RazorpayPaymentSerializer(serializers.ModelSerializer):
    """
    Serializer for RazorpayPayment model.
    """
    transaction_id = serializers.CharField(source='transaction.transaction_id', read_only=True)

    class Meta:
        model = RazorpayPayment
        fields = [
            'id', 'transaction', 'transaction_id', 'razorpay_order_id',
            'razorpay_payment_id', 'razorpay_signature', 'webhook_verified',
            'created_at', 'updated_at'
        ]


class CODPaymentSerializer(serializers.ModelSerializer):
    """
    Serializer for CODPayment model.
    """
    transaction_id = serializers.CharField(source='transaction.transaction_id', read_only=True)
    collected_by_name = serializers.CharField(source='collected_by.get_full_name', read_only=True)

    class Meta:
        model = CODPayment
        fields = [
            'id', 'transaction', 'transaction_id', 'collected_amount',
            'collected_by', 'collected_by_name', 'collected_at',
            'collection_notes', 'created_at', 'updated_at'
        ]


class PaymentWebhookSerializer(serializers.ModelSerializer):
    """
    Serializer for PaymentWebhook model.
    """
    transaction_id = serializers.CharField(source='transaction.transaction_id', read_only=True)

    class Meta:
        model = PaymentWebhook
        fields = [
            'id', 'webhook_id', 'source', 'event_type', 'payload',
            'processed', 'processing_error', 'transaction', 'transaction_id',
            'created_at'
        ]


class PaymentStatusSerializer(serializers.Serializer):
    """
    Serializer for payment status response.
    """
    transaction_id = serializers.CharField()
    status = serializers.CharField()
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    payment_method = serializers.CharField()
    gateway_payment_id = serializers.CharField(required=False)
    failure_reason = serializers.CharField(required=False)
    created_at = serializers.DateTimeField()
    completed_at = serializers.DateTimeField(required=False)


class PaymentSummarySerializer(serializers.Serializer):
    """
    Serializer for payment summary/dashboard.
    """
    total_transactions = serializers.IntegerField()
    successful_payments = serializers.IntegerField()
    failed_payments = serializers.IntegerField()
    pending_payments = serializers.IntegerField()
    total_amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    total_refunds = serializers.DecimalField(max_digits=12, decimal_places=2)
    razorpay_transactions = serializers.IntegerField()
    cod_transactions = serializers.IntegerField()
