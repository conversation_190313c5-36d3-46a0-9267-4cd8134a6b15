# 📱 Home Services Authentication API - Postman Testing Guide

## 🔗 **Base URL**
```
http://localhost:8000
```

## 📋 **Postman Environment Variables**

Create a new environment in Postman with these variables:

| Variable | Initial Value | Current Value |
|----------|---------------|---------------|
| `base_url` | `http://localhost:8000` | `http://localhost:8000` |
| `access_token` | | (will be set automatically) |
| `refresh_token` | | (will be set automatically) |
| `user_id` | | (will be set automatically) |

---

## 🔐 **Authentication Endpoints**

### 1. **Register Customer/Provider (Mobile)**

**Endpoint:** `POST {{base_url}}/api/auth/register/mobile/`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
    "mobile_number": "+************",
    "name": "<PERSON>",
    "user_type": "CUSTOMER"
}
```

**Response (201 Created):**
```json
{
    "message": "User registered successfully. OTP sent for verification.",
    "user_id": 123,
    "mobile_number": "+************",
    "requires_verification": true
}
```

**Postman Test Script:**
```javascript
if (pm.response.code === 201) {
    const response = pm.response.json();
    pm.environment.set("user_id", response.user_id);
}
```

---

### 2. **Send OTP**

**Endpoint:** `POST {{base_url}}/api/auth/otp/send/`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
    "mobile_number": "+************"
}
```

**Response (200 OK):**
```json
{
    "message": "OTP sent successfully",
    "mobile_number": "+************"
}
```

**Error Response (400 Bad Request):**
```json
{
    "mobile_number": ["Enter a valid Indian mobile number. Format: +91XXXXXXXXXX"]
}
```

---

### 3. **Resend OTP**

**Endpoint:** `POST {{base_url}}/api/auth/otp/resend/`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
    "mobile_number": "+************"
}
```

**Response (200 OK):**
```json
{
    "message": "OTP resent successfully",
    "mobile_number": "+************",
    "retry_type": "voice"
}
```

**Error Response (400 Bad Request):**
```json
{
    "mobile_number": ["Enter a valid Indian mobile number. Format: +91XXXXXXXXXX"]
}
```

---

### 4. **Verify OTP**

**Endpoint:** `POST {{base_url}}/api/auth/otp/verify/`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
    "mobile_number": "+************",
    "otp": "123456"
}
```

**Response (200 OK):**
```json
{
    "message": "OTP verified successfully",
    "user_verified": true
}
```

**Error Response (400 Bad Request - Invalid OTP):**
```json
{
    "error": "Invalid or expired OTP"
}
```

**Error Response (400 Bad Request - Validation Error):**
```json
{
    "mobile_number": ["This field is required."],
    "otp": ["This field is required."]
}
```

---

### 4. **Login with Mobile/OTP**

**Endpoint:** `POST {{base_url}}/api/auth/login/mobile/`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
    "mobile_number": "+************",
    "otp": "123456"
}
```

**Response (200 OK):**
```json
{
    "message": "Login successful",
    "user": {
        "id": 123,
        "mobile_number": "+************",
        "name": "John Doe",
        "user_type": "CUSTOMER",
        "is_verified": true,
        "date_joined": "2025-01-27T10:30:00Z"
    },
    "tokens": {
        "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
    }
}
```

**Error Response (401 Unauthorized):**
```json
{
    "error": "Invalid OTP or account locked"
}
```

**Error Response (400 Bad Request):**
```json
{
    "mobile_number": ["This field is required."],
    "otp": ["This field is required."]
}
```

**Postman Test Script:**
```javascript
if (pm.response.code === 200) {
    const response = pm.response.json();
    pm.environment.set("access_token", response.tokens.access);
    pm.environment.set("refresh_token", response.tokens.refresh);
    pm.environment.set("user_id", response.user.id);
}
```

---

### 5. **Staff Email Login**

**Endpoint:** `POST {{base_url}}/api/auth/login/email/`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
    "email": "<EMAIL>",
    "password": "admin123"
}
```

**Response (200 OK):**
```json
{
    "message": "Login successful",
    "user": {
        "id": 1,
        "email": "<EMAIL>",
        "name": "Admin User",
        "user_type": "STAFF",
        "is_verified": true,
        "date_joined": "2025-01-27T10:00:00Z"
    },
    "tokens": {
        "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
    }
}
```

**Postman Test Script:**
```javascript
if (pm.response.code === 200) {
    const response = pm.response.json();
    pm.environment.set("access_token", response.tokens.access);
    pm.environment.set("refresh_token", response.tokens.refresh);
}
```

---

## 👤 **User Profile Endpoints**

### 6. **Get User Profile**

**Endpoint:** `GET {{base_url}}/api/auth/profile/`

**Headers:**
```
Authorization: Bearer {{access_token}}
Content-Type: application/json
```

**Response (200 OK):**
```json
{
    "id": 123,
    "mobile_number": "+************",
    "name": "John Doe",
    "user_type": "CUSTOMER",
    "is_verified": true,
    "profile_picture": null,
    "date_joined": "2025-01-27T10:30:00Z"
}
```

---

### 7. **Update User Profile**

**Endpoint:** `PUT {{base_url}}/api/auth/profile/`

**Headers:**
```
Authorization: Bearer {{access_token}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
    "name": "John Updated Doe"
}
```

**Response (200 OK):**
```json
{
    "message": "Profile updated successfully",
    "user": {
        "id": 123,
        "mobile_number": "+************",
        "name": "John Updated Doe",
        "user_type": "CUSTOMER",
        "is_verified": true,
        "profile_picture": null,
        "date_joined": "2025-01-27T10:30:00Z"
    }
}
```

---

## 🏠 **Address Management Endpoints**

### 8. **Get User Addresses**

**Endpoint:** `GET {{base_url}}/api/auth/addresses/`

**Headers:**
```
Authorization: Bearer {{access_token}}
Content-Type: application/json
```

**Response (200 OK):**
```json
[
    {
        "id": 1,
        "address_type": "HOME",
        "street": "123 Main Street, Apartment 4B",
        "city": "Mumbai",
        "state": "Maharashtra",
        "zip_code": "400001",
        "landmark": "Near Central Mall",
        "is_default": true,
        "created_at": "2025-01-27T10:30:00Z"
    }
]
```

---

### 9. **Create Address**

**Endpoint:** `POST {{base_url}}/api/auth/addresses/`

**Headers:**
```
Authorization: Bearer {{access_token}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
    "address_type": "WORK",
    "street": "456 Business District, Floor 5",
    "city": "Delhi",
    "state": "Delhi",
    "zip_code": "110001",
    "landmark": "Near Metro Station",
    "is_default": false
}
```

**Response (201 Created):**
```json
{
    "message": "Address created successfully",
    "address": {
        "id": 2,
        "address_type": "WORK",
        "street": "456 Business District, Floor 5",
        "city": "Delhi",
        "state": "Delhi",
        "zip_code": "110001",
        "landmark": "Near Metro Station",
        "is_default": false,
        "created_at": "2025-01-27T11:00:00Z"
    }
}
```

---

## 🔄 **Token Management**

### 10. **Refresh Access Token**

**Endpoint:** `POST {{base_url}}/api/auth/token/refresh/`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
    "refresh": "{{refresh_token}}"
}
```

**Response (200 OK):**
```json
{
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Postman Test Script:**
```javascript
if (pm.response.code === 200) {
    const response = pm.response.json();
    pm.environment.set("access_token", response.access);
}
```

---

### 11. **Logout**

**Endpoint:** `POST {{base_url}}/api/auth/logout/`

**Headers:**
```
Authorization: Bearer {{access_token}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
    "refresh": "{{refresh_token}}"
}
```

**Response (200 OK):**
```json
{
    "message": "Logged out successfully"
}
```

---

## 🛠️ **Admin Endpoints**

### 12. **Get User Statistics (Admin Only)**

**Endpoint:** `GET {{base_url}}/api/auth/admin/user-stats/`

**Headers:**
```
Authorization: Bearer {{access_token}}
Content-Type: application/json
```

**Response (200 OK):**
```json
{
    "total_users": 150,
    "user_types": {
        "customers": 120,
        "providers": 25,
        "staff": 5
    },
    "verified_users": 140,
    "locked_accounts": 2,
    "recent_failed_attempts": 8
}
```

---

## 🧪 **Testing Workflow**

### **Complete User Registration & Login Flow:**

1. **Register Customer** → `POST /api/auth/register/mobile/`
2. **Send OTP** → `POST /api/auth/otp/send/`
3. **Verify OTP** → `POST /api/auth/otp/verify/`
4. **Login with OTP** → `POST /api/auth/login/mobile/`
5. **Get Profile** → `GET /api/auth/profile/`
6. **Create Address** → `POST /api/auth/addresses/`
7. **Update Profile** → `PUT /api/auth/profile/`
8. **Logout** → `POST /api/auth/logout/`

### **OTP Flow (Alternative):**

1. **Send OTP** → `POST /api/auth/otp/send/`
2. **Resend OTP (if needed)** → `POST /api/auth/otp/resend/`
3. **Verify OTP** → `POST /api/auth/otp/verify/`

### **Staff Login Flow:**

1. **Staff Login** → `POST /api/auth/login/email/`
2. **Get User Stats** → `GET /api/auth/admin/user-stats/`
3. **Logout** → `POST /api/auth/logout/`

---

## ⚠️ **Common Error Responses**

### **401 Unauthorized:**
```json
{
    "detail": "Authentication credentials were not provided."
}
```

### **400 Bad Request:**
```json
{
    "mobile_number": ["This field is required."],
    "name": ["This field may not be blank."]
}
```

### **403 Forbidden:**
```json
{
    "error": "Permission denied"
}
```

---

## 🔧 **Postman Collection Setup**

1. **Create Environment** with variables listed above
2. **Import requests** using the endpoints above
3. **Add test scripts** to automatically save tokens
4. **Set up authentication** to use Bearer token from environment

---

## 👤 **User Profile Endpoints**

### 6. **Get User Profile**

**Endpoint:** `GET {{base_url}}/api/auth/profile/`

**Headers:**
```
Authorization: Bearer {{access_token}}
Content-Type: application/json
```

**Response (200 OK):**
```json
{
    "id": 123,
    "mobile_number": "+************",
    "name": "John Doe",
    "user_type": "CUSTOMER",
    "is_verified": true,
    "profile_picture": null,
    "date_joined": "2025-01-27T10:30:00Z"
}
```

---

### 7. **Update User Profile**

**Endpoint:** `PUT {{base_url}}/api/auth/profile/`

**Headers:**
```
Authorization: Bearer {{access_token}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
    "name": "John Updated Doe"
}
```

**Response (200 OK):**
```json
{
    "message": "Profile updated successfully",
    "user": {
        "id": 123,
        "mobile_number": "+************",
        "name": "John Updated Doe",
        "user_type": "CUSTOMER",
        "is_verified": true,
        "profile_picture": null,
        "date_joined": "2025-01-27T10:30:00Z"
    }
}
```

---

## 🏠 **Address Management Endpoints**

### 8. **Get User Addresses**

**Endpoint:** `GET {{base_url}}/api/auth/addresses/`

**Headers:**
```
Authorization: Bearer {{access_token}}
Content-Type: application/json
```

**Response (200 OK):**
```json
[
    {
        "id": 1,
        "address_type": "HOME",
        "street": "123 Main Street, Apartment 4B",
        "city": "Mumbai",
        "state": "Maharashtra",
        "zip_code": "400001",
        "landmark": "Near Central Mall",
        "is_default": true,
        "created_at": "2025-01-27T10:30:00Z"
    }
]
```

---

### 9. **Create Address**

**Endpoint:** `POST {{base_url}}/api/auth/addresses/`

**Headers:**
```
Authorization: Bearer {{access_token}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
    "address_type": "WORK",
    "street": "456 Business District, Floor 5",
    "city": "Delhi",
    "state": "Delhi",
    "zip_code": "110001",
    "landmark": "Near Metro Station",
    "is_default": false
}
```

**Response (201 Created):**
```json
{
    "message": "Address created successfully",
    "address": {
        "id": 2,
        "address_type": "WORK",
        "street": "456 Business District, Floor 5",
        "city": "Delhi",
        "state": "Delhi",
        "zip_code": "110001",
        "landmark": "Near Metro Station",
        "is_default": false,
        "created_at": "2025-01-27T11:00:00Z"
    }
}
```

---

## 🔄 **Token Management**

### 10. **Refresh Access Token**

**Endpoint:** `POST {{base_url}}/api/auth/token/refresh/`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
    "refresh": "{{refresh_token}}"
}
```

**Response (200 OK):**
```json
{
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Postman Test Script:**
```javascript
if (pm.response.code === 200) {
    const response = pm.response.json();
    pm.environment.set("access_token", response.access);
}
```

---

### 11. **Logout**

**Endpoint:** `POST {{base_url}}/api/auth/logout/`

**Headers:**
```
Authorization: Bearer {{access_token}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
    "refresh": "{{refresh_token}}"
}
```

**Response (200 OK):**
```json
{
    "message": "Logged out successfully"
}
```

---

## 🛠️ **Admin Endpoints**

### 12. **Get User Statistics (Admin Only)**

**Endpoint:** `GET {{base_url}}/api/auth/admin/user-stats/`

**Headers:**
```
Authorization: Bearer {{access_token}}
Content-Type: application/json
```

**Response (200 OK):**
```json
{
    "total_users": 150,
    "user_types": {
        "customers": 120,
        "providers": 25,
        "staff": 5
    },
    "verified_users": 140,
    "locked_accounts": 2,
    "recent_failed_attempts": 8
}
```

---

## 🧪 **Testing Workflow**

### **Complete User Registration & Login Flow:**

1. **Register Customer** → `POST /api/auth/register/mobile/`
2. **Send OTP** → `POST /api/auth/otp/send/`
3. **Verify OTP** → `POST /api/auth/otp/verify/`
4. **Login with OTP** → `POST /api/auth/login/mobile/`
5. **Get Profile** → `GET /api/auth/profile/`
6. **Create Address** → `POST /api/auth/addresses/`
7. **Update Profile** → `PUT /api/auth/profile/`
8. **Logout** → `POST /api/auth/logout/`

### **Staff Login Flow:**

1. **Staff Login** → `POST /api/auth/login/email/`
2. **Get User Stats** → `GET /api/auth/admin/user-stats/`
3. **Logout** → `POST /api/auth/logout/`

---

## ⚠️ **Common Error Responses**

### **401 Unauthorized:**
```json
{
    "detail": "Authentication credentials were not provided."
}
```

### **400 Bad Request:**
```json
{
    "mobile_number": ["This field is required."],
    "name": ["This field may not be blank."]
}
```

### **403 Forbidden:**
```json
{
    "error": "Permission denied"
}
```

---

## 🔧 **Postman Collection Setup**

1. **Create Environment** with variables listed above
2. **Import requests** using the endpoints above
3. **Add test scripts** to automatically save tokens
4. **Set up authentication** to use Bearer token from environment

**Happy Testing! 🚀**
