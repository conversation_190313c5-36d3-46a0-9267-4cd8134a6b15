# Production Environment Configuration
# Copy this to .env and update with your production values

# Django Settings
SECRET_KEY=your-super-secret-production-key-here-change-this
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com,your-server-ip

# Database Settings (PostgreSQL)
DB_NAME=auth_service_prod
DB_USER=your_db_user
DB_PASSWORD=your_secure_db_password
DB_HOST=localhost
DB_PORT=5432

# Redis Settings (Enable for production)
USE_REDIS=True
REDIS_URL=redis://localhost:6379/1

# Rate Limiting (Enable for production)
RATELIMIT_ENABLE=True

# Security Settings (HTTPS)
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
CSRF_TRUSTED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# JWT Token Settings
JWT_ACCESS_TOKEN_LIFETIME=60
JWT_REFRESH_TOKEN_LIFETIME=10080

# MSG91 SMS Gateway Settings
MSG91_AUTH_KEY=your-actual-msg91-auth-key
MSG91_TEMPLATE_ID=your-actual-template-id

# Email Settings
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# CORS Settings
CORS_ALLOWED_ORIGINS=https://your-frontend-domain.com,https://www.your-frontend-domain.com
