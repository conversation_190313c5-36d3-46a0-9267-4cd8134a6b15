from django.db import models
from django.conf import settings
from decimal import Decimal
from django.utils import timezone
import uuid


class PaymentTransaction(models.Model):
    """
    Track all payment transactions across different payment methods.
    """
    PAYMENT_METHOD_CHOICES = [
        ('razorpay', 'Razorpay'),
        ('cod', 'Cash on Delivery'),
    ]
    
    STATUS_CHOICES = [
        ('initiated', 'Initiated'),
        ('pending', 'Pending'),
        ('success', 'Success'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
        ('refunded', 'Refunded'),
    ]

    # Transaction identification
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    transaction_id = models.CharField(max_length=100, unique=True, blank=True)
    
    # Order reference
    order_id = models.CharField(max_length=50)  # Reference to Order UUID
    order_number = models.CharField(max_length=20)
    
    # User information
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='payment_transactions'
    )
    
    # Payment details
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='INR')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='initiated')
    
    # Gateway specific fields
    gateway_transaction_id = models.CharField(max_length=100, blank=True, null=True)
    gateway_payment_id = models.CharField(max_length=100, blank=True, null=True)
    gateway_signature = models.CharField(max_length=200, blank=True, null=True)
    gateway_response = models.JSONField(blank=True, null=True)
    
    # Failure information
    failure_reason = models.TextField(blank=True, null=True)
    failure_code = models.CharField(max_length=50, blank=True, null=True)
    
    # Refund information
    refund_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    refund_reason = models.TextField(blank=True, null=True)
    refunded_at = models.DateTimeField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['order_id', 'status']),
            models.Index(fields=['user', 'status']),
            models.Index(fields=['transaction_id']),
            models.Index(fields=['gateway_payment_id']),
        ]

    def save(self, *args, **kwargs):
        if not self.transaction_id:
            self.transaction_id = self.generate_transaction_id()
        super().save(*args, **kwargs)

    def generate_transaction_id(self):
        """Generate unique transaction ID"""
        import random
        import string
        timestamp = timezone.now().strftime('%Y%m%d%H%M%S')
        random_part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
        return f"TXN{timestamp}{random_part}"

    def mark_success(self, gateway_response=None):
        """Mark transaction as successful"""
        self.status = 'success'
        self.completed_at = timezone.now()
        if gateway_response:
            self.gateway_response = gateway_response
        self.save()

    def mark_failed(self, reason=None, code=None):
        """Mark transaction as failed"""
        self.status = 'failed'
        self.failure_reason = reason
        self.failure_code = code
        self.completed_at = timezone.now()
        self.save()

    def __str__(self):
        return f"Transaction {self.transaction_id} - {self.order_number}"


class RazorpayPayment(models.Model):
    """
    Specific model for Razorpay payment details.
    """
    transaction = models.OneToOneField(
        PaymentTransaction,
        on_delete=models.CASCADE,
        related_name='razorpay_details'
    )
    
    # Razorpay specific fields
    razorpay_order_id = models.CharField(max_length=100)
    razorpay_payment_id = models.CharField(max_length=100, blank=True, null=True)
    razorpay_signature = models.CharField(max_length=200, blank=True, null=True)
    
    # Additional Razorpay data
    razorpay_response = models.JSONField(blank=True, null=True)
    webhook_verified = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Razorpay {self.razorpay_order_id}"


class CODPayment(models.Model):
    """
    Model for Cash on Delivery payments.
    """
    transaction = models.OneToOneField(
        PaymentTransaction,
        on_delete=models.CASCADE,
        related_name='cod_details'
    )
    
    # COD specific fields
    collected_amount = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        default=Decimal('0.00')
    )
    collected_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='cod_collections'
    )
    collected_at = models.DateTimeField(null=True, blank=True)
    collection_notes = models.TextField(blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def mark_collected(self, amount, collected_by, notes=None):
        """Mark COD payment as collected"""
        self.collected_amount = amount
        self.collected_by = collected_by
        self.collected_at = timezone.now()
        self.collection_notes = notes
        self.save()
        
        # Update main transaction
        self.transaction.mark_success()

    def __str__(self):
        return f"COD {self.transaction.transaction_id}"


class PaymentRefund(models.Model):
    """
    Track payment refunds.
    """
    STATUS_CHOICES = [
        ('initiated', 'Initiated'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    transaction = models.ForeignKey(
        PaymentTransaction,
        on_delete=models.CASCADE,
        related_name='refunds'
    )
    
    refund_id = models.CharField(max_length=100, unique=True, blank=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    reason = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='initiated')
    
    # Gateway specific
    gateway_refund_id = models.CharField(max_length=100, blank=True, null=True)
    gateway_response = models.JSONField(blank=True, null=True)
    
    # Processing details
    initiated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    processed_at = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def save(self, *args, **kwargs):
        if not self.refund_id:
            self.refund_id = self.generate_refund_id()
        super().save(*args, **kwargs)

    def generate_refund_id(self):
        """Generate unique refund ID"""
        import random
        import string
        timestamp = timezone.now().strftime('%Y%m%d%H%M%S')
        random_part = ''.join(random.choices(string.digits, k=4))
        return f"REF{timestamp}{random_part}"

    def __str__(self):
        return f"Refund {self.refund_id} - ₹{self.amount}"


class PaymentWebhook(models.Model):
    """
    Track payment gateway webhooks for audit and debugging.
    """
    webhook_id = models.CharField(max_length=100, unique=True, blank=True)
    source = models.CharField(max_length=50)  # razorpay, etc.
    event_type = models.CharField(max_length=100)
    
    # Webhook data
    payload = models.JSONField()
    headers = models.JSONField(blank=True, null=True)
    
    # Processing status
    processed = models.BooleanField(default=False)
    processing_error = models.TextField(blank=True, null=True)
    
    # Related transaction (if found)
    transaction = models.ForeignKey(
        PaymentTransaction,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='webhooks'
    )
    
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def save(self, *args, **kwargs):
        if not self.webhook_id:
            self.webhook_id = self.generate_webhook_id()
        super().save(*args, **kwargs)

    def generate_webhook_id(self):
        """Generate unique webhook ID"""
        import random
        import string
        timestamp = timezone.now().strftime('%Y%m%d%H%M%S')
        random_part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
        return f"WH{timestamp}{random_part}"

    def __str__(self):
        return f"Webhook {self.webhook_id} - {self.event_type}"
