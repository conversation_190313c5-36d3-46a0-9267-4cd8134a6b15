#!/usr/bin/env python
"""
Simple migration script - migrate everything to default database first.
"""
import os
import django
from django.core.management import execute_from_command_line
import glob

def simple_migrate():
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
    
    # Temporarily disable database routing
    os.environ['DISABLE_DB_ROUTING'] = '1'
    
    django.setup()
    
    print("🚀 Simple migration approach...")
    
    # Step 1: Clean up old migrations
    print("\n🗑️ Cleaning up old migration files...")
    apps = ['authentication', 'catalogue', 'cart', 'coupons', 'orders', 'payments', 'providers']
    
    for app in apps:
        migrations_dir = f"{app}/migrations"
        if os.path.exists(migrations_dir):
            for file in glob.glob(f"{migrations_dir}/0*.py"):
                os.remove(file)
                print(f"  Removed {file}")
    
    # Step 2: Create fresh migrations
    print("\n📝 Creating fresh migrations...")
    for app in apps:
        try:
            print(f"  Creating migrations for {app}...")
            execute_from_command_line(['manage.py', 'makemigrations', app, '--skip-checks'])
            print(f"  ✅ {app} migrations created")
        except Exception as e:
            print(f"  ❌ {app} error: {e}")
    
    # Step 3: Migrate everything to default database
    print("\n🗄️ Migrating all apps to default database...")
    try:
        print("  Migrating core Django apps...")
        execute_from_command_line(['manage.py', 'migrate', '--skip-checks'])
        print("  ✅ All migrations applied successfully")
    except Exception as e:
        print(f"  ❌ Migration error: {e}")
    
    print("\n🎉 Simple migration completed!")
    print("\n📋 Next steps:")
    print("1. Run: python manage.py createsuperuser")
    print("2. Run: python manage.py runserver")
    print("3. Test the APIs at: http://127.0.0.1:8000/api/")
    print("\n💡 Note: All data is in the default database for now.")
    print("   You can set up separate databases later if needed.")

if __name__ == '__main__':
    simple_migrate()
