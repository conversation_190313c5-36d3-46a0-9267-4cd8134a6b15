from django.db import models
from django.utils.text import slugify
from decimal import Decimal
from django.utils import timezone

# Try to import MPTT, if not available, use regular models
try:
    from mptt.models import MPTTModel, TreeForeignKey
    MPTT_AVAILABLE = True
except ImportError:
    MPTT_AVAILABLE = False
    # Fallback to regular Django models
    MPTTModel = models.Model
    TreeForeignKey = models.ForeignKey


class Category(MPTTModel):
    """
    Hierarchical category model using MPTT for efficient tree operations.
    Supports nested categories for service organization.
    """
    name = models.CharField(max_length=255, unique=True)
    slug = models.SlugField(max_length=255, unique=True, blank=True)
    image = models.ImageField(upload_to='category_images/', blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    parent = TreeForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children'
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    if MPTT_AVAILABLE:
        class MPTTMeta:
            order_insertion_by = ['name']

    class Meta:
        verbose_name_plural = "Categories"
        ordering = ['name']

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return f"/api/catalogue/categories/{self.slug}/"


class Service(models.Model):
    """
    Service model representing individual services offered.
    Linked to categories with pricing and timing information.
    """
    category = models.ForeignKey(
        Category,
        on_delete=models.CASCADE,
        related_name='services'
    )
    title = models.CharField(max_length=255)
    slug = models.SlugField(max_length=255, unique=True, blank=True)
    image = models.ImageField(upload_to='service_images/', blank=True, null=True)
    description = models.TextField()
    base_price = models.DecimalField(max_digits=10, decimal_places=2)
    discount_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Optional discounted price"
    )
    time_to_complete = models.DurationField(
        null=True,
        blank=True,
        help_text="Estimated time to complete the service"
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['title']
        indexes = [
            models.Index(fields=['category', 'is_active']),
            models.Index(fields=['slug']),
        ]

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

    def get_current_price(self):
        """Return the effective price (discount price if available, otherwise base price)"""
        return self.discount_price if self.discount_price is not None else self.base_price

    def get_discount_percentage(self):
        """Calculate discount percentage if discount price is set"""
        if self.discount_price and self.discount_price < self.base_price:
            return round(((self.base_price - self.discount_price) / self.base_price) * 100, 2)
        return 0

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return f"/api/catalogue/services/{self.slug}/"


class Discount(models.Model):
    """
    Discount model for managing promotional offers on categories or services.
    Supports both percentage and fixed amount discounts.
    """
    DISCOUNT_TYPE_CHOICES = [
        ('percentage', 'Percentage'),
        ('fixed', 'Fixed Amount'),
    ]

    name = models.CharField(max_length=255)
    code = models.CharField(max_length=50, unique=True, blank=True, null=True)
    discount_type = models.CharField(max_length=10, choices=DISCOUNT_TYPE_CHOICES)
    value = models.DecimalField(max_digits=5, decimal_places=2)
    start_date = models.DateTimeField(default=timezone.now)
    end_date = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)

    # Discount can apply to either category or specific service
    applies_to_category = models.ForeignKey(
        Category,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='discounts'
    )
    applies_to_service = models.ForeignKey(
        Service,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='discounts'
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
            models.CheckConstraint(
                check=models.Q(applies_to_category__isnull=False) | models.Q(applies_to_service__isnull=False),
                name='discount_must_apply_to_category_or_service'
            )
        ]
        ordering = ['-created_at']

    def clean(self):
        from django.core.exceptions import ValidationError
        if not self.applies_to_category and not self.applies_to_service:
            raise ValidationError("Discount must apply to either a category or a service.")
        if self.applies_to_category and self.applies_to_service:
            raise ValidationError("Discount cannot apply to both category and service.")

    def is_valid(self):
        """Check if discount is currently valid"""
        now = timezone.now()
        if not self.is_active:
            return False
        if self.start_date > now:
            return False
        if self.end_date and self.end_date < now:
            return False
        return True

    def calculate_discount(self, amount):
        """Calculate discount amount for given price"""
        if not self.is_valid():
            return Decimal('0.00')

        if self.discount_type == 'percentage':
            return (amount * self.value) / Decimal('100')
        else:  # fixed
            return min(self.value, amount)  # Don't exceed the original amount

    def __str__(self):
        return self.name
