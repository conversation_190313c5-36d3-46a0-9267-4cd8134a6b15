from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsA<PERSON>enticated, AllowAny
from django.shortcuts import get_object_or_404
from django.db import transaction
from catalogue.models import Service
from .models import Cart, CartItem
from .serializers import (
    CartSerializer, CartItemSerializer, AddToCartSerializer,
    UpdateCartItemSerializer, ApplyCouponSerializer, CartSummarySerializer,
    CheckoutValidationSerializer
)


def get_or_create_cart(request):
    """
    Helper function to get or create cart for authenticated or anonymous user.
    """
    if request.user.is_authenticated:
        cart, created = Cart.objects.get_or_create(
            user=request.user,
            is_active=True,
            defaults={'session_key': None}
        )
    else:
        session_key = request.session.session_key
        if not session_key:
            request.session.create()
            session_key = request.session.session_key

        cart, created = Cart.objects.get_or_create(
            session_key=session_key,
            is_active=True,
            defaults={'user': None}
        )

    return cart


class CartDetailView(generics.RetrieveAPIView):
    """
    Get current user's cart details.
    """
    serializer_class = CartSerializer
    permission_classes = [AllowAny]

    def get_object(self):
        return get_or_create_cart(self.request)


class CartSummaryView(generics.RetrieveAPIView):
    """
    Get cart summary (totals only).
    """
    serializer_class = CartSummarySerializer
    permission_classes = [AllowAny]

    def get_object(self):
        return get_or_create_cart(self.request)


@api_view(['POST'])
@permission_classes([AllowAny])
def add_to_cart(request):
    """
    Add a service to the cart.
    """
    cart = get_or_create_cart(request)

    serializer = AddToCartSerializer(
        data=request.data,
        context={'cart': cart}
    )

    if serializer.is_valid():
        service_id = serializer.validated_data['service_id']
        quantity = serializer.validated_data['quantity']

        try:
            with transaction.atomic():
                service = Service.objects.get(id=service_id, is_active=True)

                # Check if item already exists
                existing_item = CartItem.objects.filter(
                    cart=cart,
                    service=service
                ).first()

                if existing_item:
                    # Update quantity
                    existing_item.quantity += quantity
                    existing_item.save()
                    cart_item = existing_item
                else:
                    # Create new item with current pricing
                    current_price = service.get_current_price()
                    discount = service.base_price - current_price if service.discount_price else 0

                    cart_item = CartItem.objects.create(
                        cart=cart,
                        service=service,
                        quantity=quantity,
                        price_at_add=service.base_price,
                        discount_at_add=discount
                    )

                # Return updated cart
                cart_serializer = CartSerializer(cart)
                return Response({
                    'success': True,
                    'message': f'Added {service.title} to cart',
                    'cart': cart_serializer.data
                }, status=status.HTTP_201_CREATED)

        except Service.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Service not found'
            }, status=status.HTTP_404_NOT_FOUND)

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['PUT'])
@permission_classes([AllowAny])
def update_cart_item(request, item_id):
    """
    Update quantity of a cart item.
    """
    cart = get_or_create_cart(request)

    try:
        cart_item = CartItem.objects.get(id=item_id, cart=cart)
    except CartItem.DoesNotExist:
        return Response({
            'success': False,
            'error': 'Cart item not found'
        }, status=status.HTTP_404_NOT_FOUND)

    serializer = UpdateCartItemSerializer(data=request.data)

    if serializer.is_valid():
        cart_item.quantity = serializer.validated_data['quantity']
        cart_item.save()

        cart_serializer = CartSerializer(cart)
        return Response({
            'success': True,
            'message': 'Cart item updated',
            'cart': cart_serializer.data
        })

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['DELETE'])
@permission_classes([AllowAny])
def remove_cart_item(request, item_id):
    """
    Remove an item from the cart.
    """
    cart = get_or_create_cart(request)

    try:
        cart_item = CartItem.objects.get(id=item_id, cart=cart)
        service_title = cart_item.service.title
        cart_item.delete()

        cart_serializer = CartSerializer(cart)
        return Response({
            'success': True,
            'message': f'Removed {service_title} from cart',
            'cart': cart_serializer.data
        })
    except CartItem.DoesNotExist:
        return Response({
            'success': False,
            'error': 'Cart item not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['DELETE'])
@permission_classes([AllowAny])
def clear_cart(request):
    """
    Clear all items from the cart.
    """
    cart = get_or_create_cart(request)
    cart.items.all().delete()
    cart.update_totals()

    return Response({
        'success': True,
        'message': 'Cart cleared successfully'
    })


@api_view(['POST'])
@permission_classes([AllowAny])
def apply_coupon(request):
    """
    Apply a coupon code to the cart.
    This will integrate with the Coupon & Discount Service.
    """
    cart = get_or_create_cart(request)

    serializer = ApplyCouponSerializer(data=request.data)

    if serializer.is_valid():
        coupon_code = serializer.validated_data['coupon_code']

        # TODO: Integrate with Coupon & Discount Service
        # For now, just store the coupon code
        cart.coupon_code_applied = coupon_code
        cart.save()

        cart_serializer = CartSerializer(cart)
        return Response({
            'success': True,
            'message': f'Coupon {coupon_code} applied successfully',
            'cart': cart_serializer.data
        })

    return Response({
        'success': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def remove_coupon(request):
    """
    Remove applied coupon from cart.
    """
    cart = get_or_create_cart(request)

    if cart.coupon_code_applied:
        cart.coupon_code_applied = None
        cart.discount_amount = 0
        cart.save()

        cart_serializer = CartSerializer(cart)
        return Response({
            'success': True,
            'message': 'Coupon removed successfully',
            'cart': cart_serializer.data
        })

    return Response({
        'success': False,
        'message': 'No coupon applied to cart'
    }, status=status.HTTP_400_BAD_REQUEST)
