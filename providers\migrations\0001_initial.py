# Generated by Django 4.2.7 on 2025-06-13 10:29

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('catalogue', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProviderProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('business_name', models.CharField(blank=True, max_length=255, null=True)),
                ('business_type', models.CharField(choices=[('individual', 'Individual'), ('partnership', 'Partnership'), ('company', 'Company'), ('llp', 'Limited Liability Partnership')], default='individual', max_length=50)),
                ('years_of_experience', models.PositiveIntegerField(default=0)),
                ('service_areas', models.JSO<PERSON>ield(default=list, help_text='List of areas where provider offers services')),
                ('working_hours', models.JSONField(default=dict, help_text='Working hours for each day of the week')),
                ('verification_status', models.CharField(choices=[('pending', 'Pending Verification'), ('under_review', 'Under Review'), ('verified', 'Verified'), ('rejected', 'Rejected'), ('suspended', 'Suspended')], default='pending', max_length=20)),
                ('verified_at', models.DateTimeField(blank=True, null=True)),
                ('average_rating', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=3)),
                ('total_reviews', models.PositiveIntegerField(default=0)),
                ('total_orders_completed', models.PositiveIntegerField(default=0)),
                ('commission_rate', models.DecimalField(decimal_places=2, default=Decimal('15.00'), help_text='Commission percentage charged by platform', max_digits=5)),
                ('is_available', models.BooleanField(default=True)),
                ('accepts_new_orders', models.BooleanField(default=True)),
                ('profile_description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('services_offered', models.ManyToManyField(blank=True, help_text='Specific services this provider offers', to='catalogue.service')),
                ('specializations', models.ManyToManyField(blank=True, help_text='Categories this provider specializes in', to='catalogue.category')),
                ('user', models.OneToOneField(limit_choices_to={'user_type': 'provider'}, on_delete=django.db.models.deletion.CASCADE, related_name='provider_profile', to=settings.AUTH_USER_MODEL)),
                ('verified_by', models.ForeignKey(blank=True, limit_choices_to={'user_type': 'staff'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_providers', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProviderPayoutRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('processing', 'Processing'), ('completed', 'Completed'), ('rejected', 'Rejected'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('transaction_id', models.CharField(blank=True, max_length=100, null=True)),
                ('gateway_response', models.JSONField(blank=True, null=True)),
                ('provider_notes', models.TextField(blank=True, null=True)),
                ('admin_notes', models.TextField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('processed_by', models.ForeignKey(blank=True, limit_choices_to={'user_type': 'staff'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_payouts', to=settings.AUTH_USER_MODEL)),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payout_requests', to='providers.providerprofile')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProviderBankDetails',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_holder_name', models.CharField(max_length=255)),
                ('account_number', models.CharField(max_length=20)),
                ('ifsc_code', models.CharField(max_length=11, validators=[django.core.validators.RegexValidator(message='Enter a valid IFSC code', regex='^[A-Z]{4}0[A-Z0-9]{6}$')])),
                ('bank_name', models.CharField(max_length=255)),
                ('branch_name', models.CharField(max_length=255)),
                ('upi_id', models.CharField(blank=True, max_length=100, null=True, validators=[django.core.validators.RegexValidator(message='Enter a valid UPI ID', regex='^[\\w\\.-]+@[\\w\\.-]+$')])),
                ('is_verified', models.BooleanField(default=False)),
                ('verified_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('provider', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='bank_details', to='providers.providerprofile')),
                ('verified_by', models.ForeignKey(blank=True, limit_choices_to={'user_type': 'staff'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_bank_details', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ProviderDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('aadhaar', 'Aadhaar Card'), ('pan', 'PAN Card'), ('bank_statement', 'Bank Statement'), ('address_proof', 'Address Proof'), ('business_license', 'Business License'), ('experience_certificate', 'Experience Certificate'), ('other', 'Other')], max_length=30)),
                ('document_file', models.FileField(upload_to='provider_documents/')),
                ('document_number', models.CharField(blank=True, max_length=100, null=True)),
                ('verification_status', models.CharField(choices=[('pending', 'Pending Review'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('expired', 'Expired')], default='pending', max_length=20)),
                ('verified_at', models.DateTimeField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True, null=True)),
                ('issued_date', models.DateField(blank=True, null=True)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='providers.providerprofile')),
                ('verified_by', models.ForeignKey(blank=True, limit_choices_to={'user_type': 'staff'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_documents', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-uploaded_at'],
                'unique_together': {('provider', 'document_type')},
            },
        ),
        migrations.CreateModel(
            name='ProviderAvailability',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('is_available', models.BooleanField(default=True)),
                ('is_booked', models.BooleanField(default=False)),
                ('booked_order_id', models.CharField(blank=True, max_length=50, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availability_slots', to='providers.providerprofile')),
            ],
            options={
                'ordering': ['date', 'start_time'],
                'unique_together': {('provider', 'date', 'start_time')},
            },
        ),
    ]
