# Generated by Django 4.2.7 on 2025-06-13 10:29

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentTransaction',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('transaction_id', models.CharField(blank=True, max_length=100, unique=True)),
                ('order_id', models.CharField(max_length=50)),
                ('order_number', models.Char<PERSON>ield(max_length=20)),
                ('payment_method', models.CharField(choices=[('razorpay', 'Razorpay'), ('cod', 'Cash on Delivery')], max_length=20)),
                ('amount', models.Decimal<PERSON>ield(decimal_places=2, max_digits=10)),
                ('currency', models.Char<PERSON><PERSON>(default='INR', max_length=3)),
                ('status', models.Char<PERSON>ield(choices=[('initiated', 'Initiated'), ('pending', 'Pending'), ('success', 'Success'), ('failed', 'Failed'), ('cancelled', 'Cancelled'), ('refunded', 'Refunded')], default='initiated', max_length=20)),
                ('gateway_transaction_id', models.CharField(blank=True, max_length=100, null=True)),
                ('gateway_payment_id', models.CharField(blank=True, max_length=100, null=True)),
                ('gateway_signature', models.CharField(blank=True, max_length=200, null=True)),
                ('gateway_response', models.JSONField(blank=True, null=True)),
                ('failure_reason', models.TextField(blank=True, null=True)),
                ('failure_code', models.CharField(blank=True, max_length=50, null=True)),
                ('refund_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('refund_reason', models.TextField(blank=True, null=True)),
                ('refunded_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_transactions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RazorpayPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('razorpay_order_id', models.CharField(max_length=100)),
                ('razorpay_payment_id', models.CharField(blank=True, max_length=100, null=True)),
                ('razorpay_signature', models.CharField(blank=True, max_length=200, null=True)),
                ('razorpay_response', models.JSONField(blank=True, null=True)),
                ('webhook_verified', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('transaction', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='razorpay_details', to='payments.paymenttransaction')),
            ],
        ),
        migrations.CreateModel(
            name='PaymentWebhook',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('webhook_id', models.CharField(blank=True, max_length=100, unique=True)),
                ('source', models.CharField(max_length=50)),
                ('event_type', models.CharField(max_length=100)),
                ('payload', models.JSONField()),
                ('headers', models.JSONField(blank=True, null=True)),
                ('processed', models.BooleanField(default=False)),
                ('processing_error', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('transaction', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='webhooks', to='payments.paymenttransaction')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PaymentRefund',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('refund_id', models.CharField(blank=True, max_length=100, unique=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('reason', models.TextField()),
                ('status', models.CharField(choices=[('initiated', 'Initiated'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed')], default='initiated', max_length=20)),
                ('gateway_refund_id', models.CharField(blank=True, max_length=100, null=True)),
                ('gateway_response', models.JSONField(blank=True, null=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('initiated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('transaction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='refunds', to='payments.paymenttransaction')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CODPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('collected_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('collected_at', models.DateTimeField(blank=True, null=True)),
                ('collection_notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('collected_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='cod_collections', to=settings.AUTH_USER_MODEL)),
                ('transaction', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='cod_details', to='payments.paymenttransaction')),
            ],
        ),
        migrations.AddIndex(
            model_name='paymenttransaction',
            index=models.Index(fields=['order_id', 'status'], name='payments_pa_order_i_ed562a_idx'),
        ),
        migrations.AddIndex(
            model_name='paymenttransaction',
            index=models.Index(fields=['user', 'status'], name='payments_pa_user_id_92d873_idx'),
        ),
        migrations.AddIndex(
            model_name='paymenttransaction',
            index=models.Index(fields=['transaction_id'], name='payments_pa_transac_c1fd68_idx'),
        ),
        migrations.AddIndex(
            model_name='paymenttransaction',
            index=models.Index(fields=['gateway_payment_id'], name='payments_pa_gateway_076725_idx'),
        ),
    ]
