from django.urls import path
from . import views

app_name = 'catalogue'

urlpatterns = [
    # Category URLs
    path('categories/', views.CategoryListCreateView.as_view(), name='category-list-create'),
    path('categories/tree/', views.CategoryTreeView.as_view(), name='category-tree'),
    path('categories/<slug:slug>/', views.CategoryDetailView.as_view(), name='category-detail'),
    path('categories/<slug:category_slug>/services/', views.category_services, name='category-services'),
    
    # Service URLs
    path('services/', views.ServiceListCreateView.as_view(), name='service-list-create'),
    path('services/search/', views.search_services, name='service-search'),
    path('services/<slug:slug>/', views.ServiceDetailView.as_view(), name='service-detail'),
    
    # Discount URLs (Admin only)
    path('discounts/', views.DiscountListCreateView.as_view(), name='discount-list-create'),
    path('discounts/<int:pk>/', views.DiscountDetailView.as_view(), name='discount-detail'),
]
