"""
URL configuration for home_services project.
Home Service Platform - Authentication Service
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView

urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),

    # API endpoints
    path('api/auth/', include('authentication.urls')),
    path('api/catalogue/', include('catalogue.urls')),
    path('api/cart/', include('cart.urls')),
    path('api/coupons/', include('coupons.urls')),
    path('api/orders/', include('orders.urls')),

    # API Documentation
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

# Custom error handlers
handler400 = 'home_services.error_handlers.handler400'
handler403 = 'home_services.error_handlers.handler403'
handler404 = 'home_services.error_handlers.handler404'
handler500 = 'home_services.error_handlers.handler500'
