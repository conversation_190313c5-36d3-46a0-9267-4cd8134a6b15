#!/usr/bin/env python
"""
Complete the missing migrations for all databases.
"""
import os
import django
from django.core.management import execute_from_command_line

def complete_migration():
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'home_services.settings')
    django.setup()
    
    print("🚀 Completing Multi-Database Migration...")
    
    # Database and app mapping
    migrations_needed = {
        'default': {
            'apps': ['contenttypes', 'auth', 'admin', 'sessions', 'authentication'],
            'db_name': 'home_services_auth'
        },
        'catalogue_db': {
            'apps': ['contenttypes', 'auth', 'catalogue'],  # catalogue already exists
            'db_name': 'home_services_catalogue'
        },
        'cart_db': {
            'apps': ['contenttypes', 'auth', 'cart'],
            'db_name': 'home_services_cart'
        },
        'coupons_db': {
            'apps': ['contenttypes', 'auth', 'coupons'],
            'db_name': 'home_services_coupons'
        },
        'orders_db': {
            'apps': ['contenttypes', 'auth', 'orders'],
            'db_name': 'home_services_orders'
        },
        'payments_db': {
            'apps': ['contenttypes', 'auth', 'payments'],
            'db_name': 'home_services_payments'
        },
        'providers_db': {
            'apps': ['contenttypes', 'auth', 'providers'],
            'db_name': 'home_services_providers'
        }
    }
    
    for db_alias, config in migrations_needed.items():
        print(f"\n📊 Migrating {db_alias} ({config['db_name']})...")
        
        for app in config['apps']:
            try:
                print(f"  🔧 Migrating {app}...")
                execute_from_command_line([
                    'manage.py', 'migrate', app,
                    f'--database={db_alias}',
                    '--skip-checks',
                    '--verbosity=1'
                ])
                print(f"  ✅ {app} completed")
            except Exception as e:
                print(f"  ⚠️ {app} issue: {str(e)[:100]}...")
                continue
        
        print(f"✅ {db_alias} migration completed")
    
    print("\n🎉 All database migrations completed!")
    
    # Verify the results
    print("\n🔍 Verifying migration results...")
    try:
        execute_from_command_line(['manage.py', 'showmigrations', '--skip-checks'])
    except Exception as e:
        print(f"Verification error: {e}")
    
    print("\n📋 Next steps:")
    print("1. Run: python manage.py createsuperuser")
    print("2. Run: python manage.py runserver")
    print("3. Test APIs: http://127.0.0.1:8000/api/")

if __name__ == '__main__':
    complete_migration()
